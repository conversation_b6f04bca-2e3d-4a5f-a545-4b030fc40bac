package connection

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"websocket-hub/internal/device"
	"websocket-hub/internal/protocol"
)

// Connection WebSocket连接封装
type Connection struct {
	ID         string                 // 连接ID（设备ID）
	Conn       *websocket.Conn        // WebSocket连接
	Device     *device.DeviceInfo     // 设备信息
	Tags       map[string]bool        // 连接标签（用于快速查找）
	Send<PERSON>han   chan []byte            // 发送消息通道
	CloseChan  chan struct{}          // 关闭通道
	LastPing   time.Time              // 最后ping时间
	LastPong   time.Time              // 最后pong时间
	mutex      sync.RWMutex           // 读写锁
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	connections    map[string]*Connection    // deviceID -> Connection
	tagIndex       map[string]map[string]bool // tag -> deviceID -> bool
	deviceManager  *device.DeviceManager     // 设备管理器
	upgrader       websocket.Upgrader        // WebSocket升级器
	logger         *zap.Logger               // 日志记录器
	mutex          sync.RWMutex              // 读写锁
	
	// 配置参数
	writeTimeout   time.Duration
	readTimeout    time.Duration
	pingInterval   time.Duration
	maxMessageSize int64
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(deviceManager *device.DeviceManager, logger *zap.Logger) *ConnectionManager {
	return &ConnectionManager{
		connections:   make(map[string]*Connection),
		tagIndex:      make(map[string]map[string]bool),
		deviceManager: deviceManager,
		logger:        logger,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该实现适当的Origin检查
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		writeTimeout:   10 * time.Second,
		readTimeout:    60 * time.Second,
		pingInterval:   30 * time.Second,
		maxMessageSize: 1024 * 1024, // 1MB
	}
}

// HandleWebSocket 处理WebSocket连接
func (cm *ConnectionManager) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	// 升级HTTP连接为WebSocket
	conn, err := cm.upgrader.Upgrade(w, r, nil)
	if err != nil {
		cm.logger.Error("Failed to upgrade connection", zap.Error(err))
		return
	}

	// 设置连接参数
	conn.SetReadLimit(cm.maxMessageSize)
	conn.SetReadDeadline(time.Now().Add(cm.readTimeout))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(cm.readTimeout))
		return nil
	})

	// 等待握手消息
	if err := cm.handleHandshake(conn, r); err != nil {
		cm.logger.Error("Handshake failed", zap.Error(err))
		conn.Close()
		return
	}
}

// handleHandshake 处理握手过程
func (cm *ConnectionManager) handleHandshake(conn *websocket.Conn, r *http.Request) error {
	// 设置握手超时
	conn.SetReadDeadline(time.Now().Add(10 * time.Second))

	// 读取握手消息
	_, data, err := conn.ReadMessage()
	if err != nil {
		return fmt.Errorf("failed to read handshake message: %w", err)
	}

	// 解析握手消息
	var msg protocol.Message
	if err := msg.Unmarshal(data); err != nil {
		return fmt.Errorf("failed to unmarshal handshake message: %w", err)
	}

	if msg.GetType() != protocol.MsgTypeHandshake {
		return fmt.Errorf("expected handshake message, got %v", msg.GetType())
	}

	// 解析握手载荷
	var handshake protocol.HandshakePayload
	if err := json.Unmarshal(msg.GetPayload(), &handshake); err != nil {
		return fmt.Errorf("failed to unmarshal handshake payload: %w", err)
	}

	// 验证或生成设备ID
	var deviceInfo *device.DeviceInfo
	if handshake.DeviceID != "" {
		// 验证现有设备ID
		if err := cm.deviceManager.ValidateDevice(handshake.DeviceID); err != nil {
			return fmt.Errorf("invalid device ID: %w", err)
		}
		
		// 获取设备信息
		if dev, exists := cm.deviceManager.GetDevice(handshake.DeviceID); exists {
			deviceInfo = dev
			// 更新标签
			deviceInfo.Tags = handshake.Tags
		} else {
			return fmt.Errorf("device not found: %s", handshake.DeviceID)
		}
	} else {
		// 生成新设备ID
		var err error
		deviceInfo, err = cm.deviceManager.RegisterDevice(
			handshake.Tags,
			r.RemoteAddr,
			r.UserAgent(),
		)
		if err != nil {
			return fmt.Errorf("failed to register device: %w", err)
		}
	}

	// 创建连接对象
	connection := &Connection{
		ID:        deviceInfo.ID,
		Conn:      conn,
		Device:    deviceInfo,
		Tags:      make(map[string]bool),
		SendChan:  make(chan []byte, 256),
		CloseChan: make(chan struct{}),
		LastPing:  time.Now(),
		LastPong:  time.Now(),
	}

	// 设置标签索引
	for _, tag := range handshake.Tags {
		connection.Tags[tag] = true
	}

	// 注册连接
	cm.registerConnection(connection)

	// 发送握手响应
	respPayload, _ := json.Marshal(map[string]interface{}{
		"device_id": deviceInfo.ID,
		"status":    "connected",
		"timestamp": time.Now().Unix(),
	})
	respMsg := protocol.NewMessage(protocol.MsgTypeHandshakeResp, respPayload)
	respData, _ := respMsg.Marshal()

	if err := conn.WriteMessage(websocket.BinaryMessage, respData); err != nil {
		cm.unregisterConnection(connection.ID)
		return fmt.Errorf("failed to send handshake response: %w", err)
	}

	// 启动连接处理协程
	go cm.handleConnection(connection)
	go cm.handleConnectionWrite(connection)
	go cm.handleConnectionPing(connection)

	cm.logger.Info("Client connected",
		zap.String("device_id", deviceInfo.ID),
		zap.Strings("tags", handshake.Tags),
		zap.String("remote_addr", r.RemoteAddr),
	)

	return nil
}

// registerConnection 注册连接
func (cm *ConnectionManager) registerConnection(conn *Connection) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 如果设备已连接，关闭旧连接
	if oldConn, exists := cm.connections[conn.ID]; exists {
		close(oldConn.CloseChan)
	}

	// 注册新连接
	cm.connections[conn.ID] = conn

	// 更新标签索引
	for tag := range conn.Tags {
		if cm.tagIndex[tag] == nil {
			cm.tagIndex[tag] = make(map[string]bool)
		}
		cm.tagIndex[tag][conn.ID] = true
	}
}

// unregisterConnection 注销连接
func (cm *ConnectionManager) unregisterConnection(deviceID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	conn, exists := cm.connections[deviceID]
	if !exists {
		return
	}

	// 从标签索引中移除
	for tag := range conn.Tags {
		if tagDevices, exists := cm.tagIndex[tag]; exists {
			delete(tagDevices, deviceID)
			if len(tagDevices) == 0 {
				delete(cm.tagIndex, tag)
			}
		}
	}

	// 移除连接
	delete(cm.connections, deviceID)

	// 从设备管理器中移除
	cm.deviceManager.RemoveDevice(deviceID)

	cm.logger.Info("Client disconnected", zap.String("device_id", deviceID))
}

// handleConnection 处理连接读取
func (cm *ConnectionManager) handleConnection(conn *Connection) {
	defer func() {
		conn.Conn.Close()
		cm.unregisterConnection(conn.ID)
	}()

	for {
		select {
		case <-conn.CloseChan:
			return
		default:
			// 读取消息
			_, data, err := conn.Conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					cm.logger.Error("WebSocket error", zap.Error(err))
				}
				return
			}

			// 处理消息
			if err := cm.handleMessage(conn, data); err != nil {
				cm.logger.Error("Failed to handle message", zap.Error(err))
			}

			// 更新最后活跃时间
			cm.deviceManager.UpdateLastSeen(conn.ID)
		}
	}
}

// handleConnectionWrite 处理连接写入
func (cm *ConnectionManager) handleConnectionWrite(conn *Connection) {
	ticker := time.NewTicker(54 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case message, ok := <-conn.SendChan:
			conn.Conn.SetWriteDeadline(time.Now().Add(cm.writeTimeout))
			if !ok {
				conn.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := conn.Conn.WriteMessage(websocket.BinaryMessage, message); err != nil {
				return
			}

		case <-ticker.C:
			conn.Conn.SetWriteDeadline(time.Now().Add(cm.writeTimeout))
			if err := conn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}

		case <-conn.CloseChan:
			return
		}
	}
}

// handleConnectionPing 处理连接心跳
func (cm *ConnectionManager) handleConnectionPing(conn *Connection) {
	ticker := time.NewTicker(cm.pingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 发送心跳消息
			heartbeatMsg := protocol.NewMessage(protocol.MsgTypeHeartbeat, nil)
			data, _ := heartbeatMsg.Marshal()

			select {
			case conn.SendChan <- data:
				conn.mutex.Lock()
				conn.LastPing = time.Now()
				conn.mutex.Unlock()
			default:
				// 发送通道满，连接可能有问题
				return
			}

		case <-conn.CloseChan:
			return
		}
	}
}

// handleMessage 处理接收到的消息
func (cm *ConnectionManager) handleMessage(conn *Connection, data []byte) error {
	var msg protocol.Message
	if err := msg.Unmarshal(data); err != nil {
		return fmt.Errorf("failed to unmarshal message: %w", err)
	}

	switch msg.GetType() {
	case protocol.MsgTypeHeartbeat:
		// 心跳响应
		conn.mutex.Lock()
		conn.LastPong = time.Now()
		conn.mutex.Unlock()
		return nil

	case protocol.MsgTypeUnicast:
		return cm.handleUnicastMessage(conn, &msg)

	case protocol.MsgTypeBroadcast:
		return cm.handleBroadcastMessage(conn, &msg)

	case protocol.MsgTypeTagcast:
		return cm.handleTagcastMessage(conn, &msg)

	case protocol.MsgTypeDisconnect:
		close(conn.CloseChan)
		return nil

	default:
		return fmt.Errorf("unsupported message type: %v", msg.GetType())
	}
}

// handleUnicastMessage 处理单播消息
func (cm *ConnectionManager) handleUnicastMessage(sender *Connection, msg *protocol.Message) error {
	var payload protocol.UnicastPayload
	if err := json.Unmarshal(msg.GetPayload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal unicast payload: %w", err)
	}

	cm.mutex.RLock()
	targetConn, exists := cm.connections[payload.TargetDeviceID]
	cm.mutex.RUnlock()

	if !exists {
		// 目标设备不在线，发送错误响应
		errorPayload, _ := json.Marshal(protocol.ErrorPayload{
			Code:    404,
			Message: "Target device not found",
		})
		errorMsg := protocol.NewMessage(protocol.MsgTypeError, errorPayload)
		errorData, _ := errorMsg.Marshal()

		select {
		case sender.SendChan <- errorData:
		default:
		}
		return nil
	}

	// 转发消息到目标设备
	select {
	case targetConn.SendChan <- msg.GetPayload():
	default:
		// 目标设备发送队列满
		errorPayload, _ := json.Marshal(protocol.ErrorPayload{
			Code:    503,
			Message: "Target device busy",
		})
		errorMsg := protocol.NewMessage(protocol.MsgTypeError, errorPayload)
		errorData, _ := errorMsg.Marshal()

		select {
		case sender.SendChan <- errorData:
		default:
		}
	}

	return nil
}

// handleBroadcastMessage 处理广播消息
func (cm *ConnectionManager) handleBroadcastMessage(sender *Connection, msg *protocol.Message) error {
	var payload protocol.BroadcastPayload
	if err := json.Unmarshal(msg.GetPayload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal broadcast payload: %w", err)
	}

	cm.mutex.RLock()
	connections := make([]*Connection, 0, len(cm.connections))
	for _, conn := range cm.connections {
		if conn.ID != sender.ID { // 不发送给自己
			connections = append(connections, conn)
		}
	}
	cm.mutex.RUnlock()

	// 广播消息
	for _, conn := range connections {
		select {
		case conn.SendChan <- payload.Data:
		default:
			// 跳过发送队列满的连接
		}
	}

	return nil
}

// handleTagcastMessage 处理标签组播消息
func (cm *ConnectionManager) handleTagcastMessage(sender *Connection, msg *protocol.Message) error {
	var payload protocol.TagcastPayload
	if err := json.Unmarshal(msg.GetPayload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal tagcast payload: %w", err)
	}

	cm.mutex.RLock()
	targetDevices := make(map[string]bool)

	// 根据标签找到目标设备
	for _, tag := range payload.Tags {
		if devices, exists := cm.tagIndex[tag]; exists {
			for deviceID := range devices {
				if deviceID != sender.ID { // 不发送给自己
					targetDevices[deviceID] = true
				}
			}
		}
	}

	// 获取目标连接
	var targetConnections []*Connection
	for deviceID := range targetDevices {
		if conn, exists := cm.connections[deviceID]; exists {
			targetConnections = append(targetConnections, conn)
		}
	}
	cm.mutex.RUnlock()

	// 发送消息到目标设备
	for _, conn := range targetConnections {
		select {
		case conn.SendChan <- payload.Data:
		default:
			// 跳过发送队列满的连接
		}
	}

	return nil
}

// SendToDevice 发送消息到指定设备
func (cm *ConnectionManager) SendToDevice(deviceID string, data []byte) error {
	cm.mutex.RLock()
	conn, exists := cm.connections[deviceID]
	cm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("device not connected: %s", deviceID)
	}

	select {
	case conn.SendChan <- data:
		return nil
	default:
		return fmt.Errorf("device send queue full: %s", deviceID)
	}
}

// SendToTag 发送消息到指定标签的所有设备
func (cm *ConnectionManager) SendToTag(tag string, data []byte) int {
	cm.mutex.RLock()
	devices, exists := cm.tagIndex[tag]
	if !exists {
		cm.mutex.RUnlock()
		return 0
	}

	var targetConnections []*Connection
	for deviceID := range devices {
		if conn, exists := cm.connections[deviceID]; exists {
			targetConnections = append(targetConnections, conn)
		}
	}
	cm.mutex.RUnlock()

	sent := 0
	for _, conn := range targetConnections {
		select {
		case conn.SendChan <- data:
			sent++
		default:
			// 跳过发送队列满的连接
		}
	}

	return sent
}

// Broadcast 广播消息到所有连接
func (cm *ConnectionManager) Broadcast(data []byte) int {
	cm.mutex.RLock()
	connections := make([]*Connection, 0, len(cm.connections))
	for _, conn := range cm.connections {
		connections = append(connections, conn)
	}
	cm.mutex.RUnlock()

	sent := 0
	for _, conn := range connections {
		select {
		case conn.SendChan <- data:
			sent++
		default:
			// 跳过发送队列满的连接
		}
	}

	return sent
}

// GetConnectionCount 获取连接数量
func (cm *ConnectionManager) GetConnectionCount() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return len(cm.connections)
}

// GetConnectionsByTag 获取指定标签的连接数量
func (cm *ConnectionManager) GetConnectionsByTag(tag string) int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if devices, exists := cm.tagIndex[tag]; exists {
		return len(devices)
	}
	return 0
}

// GetConnectionInfo 获取连接信息
func (cm *ConnectionManager) GetConnectionInfo(deviceID string) (*Connection, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	conn, exists := cm.connections[deviceID]
	return conn, exists
}

// CloseConnection 关闭指定连接
func (cm *ConnectionManager) CloseConnection(deviceID string) error {
	cm.mutex.RLock()
	conn, exists := cm.connections[deviceID]
	cm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("connection not found: %s", deviceID)
	}

	close(conn.CloseChan)
	return nil
}

// Shutdown 关闭所有连接
func (cm *ConnectionManager) Shutdown(ctx context.Context) error {
	cm.mutex.Lock()
	connections := make([]*Connection, 0, len(cm.connections))
	for _, conn := range cm.connections {
		connections = append(connections, conn)
	}
	cm.mutex.Unlock()

	// 关闭所有连接
	for _, conn := range connections {
		close(conn.CloseChan)
	}

	// 等待所有连接关闭或超时
	done := make(chan struct{})
	go func() {
		for {
			cm.mutex.RLock()
			count := len(cm.connections)
			cm.mutex.RUnlock()

			if count == 0 {
				close(done)
				return
			}
			time.Sleep(100 * time.Millisecond)
		}
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// GetDeviceManager 获取设备管理器
func (cm *ConnectionManager) GetDeviceManager() *device.DeviceManager {
	return cm.deviceManager
}
