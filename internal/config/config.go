package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `json:"server"`
	Kafka    KafkaConfig    `json:"kafka"`
	Device   DeviceConfig   `json:"device"`
	WebSocket WebSocketConfig `json:"websocket"`
	Log      LogConfig      `json:"log"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	NodeID   string `json:"node_id"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Debug    bool   `json:"debug"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Enabled bool     `json:"enabled"`
	Brokers []string `json:"brokers"`
	Topic   string   `json:"topic"`
	GroupID string   `json:"group_id"`
}

// DeviceConfig 设备配置
type DeviceConfig struct {
	SecretKey          string        `json:"secret_key"`
	MaxAge             time.Duration `json:"max_age"`
	CleanupInterval    time.Duration `json:"cleanup_interval"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	ReadTimeout      time.Duration `json:"read_timeout"`
	WriteTimeout     time.Duration `json:"write_timeout"`
	PingInterval     time.Duration `json:"ping_interval"`
	MaxMessageSize   int64         `json:"max_message_size"`
	ReadBufferSize   int           `json:"read_buffer_size"`
	WriteBufferSize  int           `json:"write_buffer_size"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level"`
	Format     string `json:"format"` // json, console
	Output     string `json:"output"` // stdout, stderr, file path
	MaxSize    int    `json:"max_size"`    // MB
	MaxBackups int    `json:"max_backups"`
	MaxAge     int    `json:"max_age"`     // days
	Compress   bool   `json:"compress"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			NodeID: generateNodeID(),
			Host:   "0.0.0.0",
			Port:   8080,
			Debug:  false,
		},
		Kafka: KafkaConfig{
			Enabled: false,
			Brokers: []string{"localhost:9092"},
			Topic:   "websocket-hub",
			GroupID: "websocket-hub-group",
		},
		Device: DeviceConfig{
			SecretKey:       "default-secret-key-change-in-production",
			MaxAge:          24 * time.Hour,
			CleanupInterval: 1 * time.Hour,
		},
		WebSocket: WebSocketConfig{
			ReadTimeout:     60 * time.Second,
			WriteTimeout:    10 * time.Second,
			PingInterval:    30 * time.Second,
			MaxMessageSize:  1024 * 1024, // 1MB
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		Log: LogConfig{
			Level:      "info",
			Format:     "json",
			Output:     "stdout",
			MaxSize:    100,
			MaxBackups: 3,
			MaxAge:     28,
			Compress:   true,
		},
	}
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	config := DefaultConfig()

	// 如果配置文件不存在，返回默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return config, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析JSON配置
	if err := json.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return config, nil
}

// LoadConfigFromEnv 从环境变量加载配置
func LoadConfigFromEnv() *Config {
	config := DefaultConfig()

	// 服务器配置
	if nodeID := os.Getenv("WS_NODE_ID"); nodeID != "" {
		config.Server.NodeID = nodeID
	}
	if host := os.Getenv("WS_HOST"); host != "" {
		config.Server.Host = host
	}
	if port := os.Getenv("WS_PORT"); port != "" {
		if p, err := parsePort(port); err == nil {
			config.Server.Port = p
		}
	}
	if debug := os.Getenv("WS_DEBUG"); debug == "true" {
		config.Server.Debug = true
	}

	// Kafka配置
	if enabled := os.Getenv("KAFKA_ENABLED"); enabled == "true" {
		config.Kafka.Enabled = true
	}
	if brokers := os.Getenv("KAFKA_BROKERS"); brokers != "" {
		config.Kafka.Brokers = strings.Split(brokers, ",")
	}
	if topic := os.Getenv("KAFKA_TOPIC"); topic != "" {
		config.Kafka.Topic = topic
	}
	if groupID := os.Getenv("KAFKA_GROUP_ID"); groupID != "" {
		config.Kafka.GroupID = groupID
	}

	// 设备配置
	if secretKey := os.Getenv("DEVICE_SECRET_KEY"); secretKey != "" {
		config.Device.SecretKey = secretKey
	}

	// 日志配置
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Log.Level = level
	}
	if format := os.Getenv("LOG_FORMAT"); format != "" {
		config.Log.Format = format
	}
	if output := os.Getenv("LOG_OUTPUT"); output != "" {
		config.Log.Output = output
	}

	return config
}

// SaveConfig 保存配置到文件
func (c *Config) SaveConfig(configPath string) error {
	// 创建目录
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// 序列化配置
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证服务器配置
	if c.Server.NodeID == "" {
		return fmt.Errorf("server.node_id is required")
	}
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("server.port must be between 1 and 65535")
	}

	// 验证Kafka配置
	if c.Kafka.Enabled {
		if len(c.Kafka.Brokers) == 0 {
			return fmt.Errorf("kafka.brokers is required when kafka is enabled")
		}
		if c.Kafka.Topic == "" {
			return fmt.Errorf("kafka.topic is required when kafka is enabled")
		}
		if c.Kafka.GroupID == "" {
			return fmt.Errorf("kafka.group_id is required when kafka is enabled")
		}
	}

	// 验证设备配置
	if c.Device.SecretKey == "" {
		return fmt.Errorf("device.secret_key is required")
	}
	if c.Device.MaxAge <= 0 {
		return fmt.Errorf("device.max_age must be positive")
	}

	// 验证WebSocket配置
	if c.WebSocket.ReadTimeout <= 0 {
		return fmt.Errorf("websocket.read_timeout must be positive")
	}
	if c.WebSocket.WriteTimeout <= 0 {
		return fmt.Errorf("websocket.write_timeout must be positive")
	}
	if c.WebSocket.MaxMessageSize <= 0 {
		return fmt.Errorf("websocket.max_message_size must be positive")
	}

	// 验证日志配置
	validLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !contains(validLevels, c.Log.Level) {
		return fmt.Errorf("log.level must be one of: %s", strings.Join(validLevels, ", "))
	}

	validFormats := []string{"json", "console"}
	if !contains(validFormats, c.Log.Format) {
		return fmt.Errorf("log.format must be one of: %s", strings.Join(validFormats, ", "))
	}

	return nil
}

// GetServerAddress 获取服务器地址
func (c *Config) GetServerAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// IsKafkaEnabled 检查Kafka是否启用
func (c *Config) IsKafkaEnabled() bool {
	return c.Kafka.Enabled
}

// generateNodeID 生成节点ID
func generateNodeID() string {
	hostname, _ := os.Hostname()
	if hostname == "" {
		hostname = "unknown"
	}
	return fmt.Sprintf("ws-node-%s-%d", hostname, time.Now().Unix())
}

// parsePort 解析端口号
func parsePort(portStr string) (int, error) {
	var port int
	if _, err := fmt.Sscanf(portStr, "%d", &port); err != nil {
		return 0, err
	}
	if port <= 0 || port > 65535 {
		return 0, fmt.Errorf("invalid port: %d", port)
	}
	return port, nil
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
