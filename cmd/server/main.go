package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"

	"websocket-hub/internal/api"
	"websocket-hub/internal/config"
	"websocket-hub/internal/device"
	"websocket-hub/internal/hub"
	"websocket-hub/internal/kafka"
	"websocket-hub/internal/logger"
)

var (
	configPath = flag.String("config", "config.json", "Path to configuration file")
	version    = "1.0.0"
	buildTime  = "unknown"
	gitCommit  = "unknown"
)

func main() {
	flag.Parse()

	// 打印版本信息
	fmt.Printf("WebSocket Hub Server v%s\n", version)
	fmt.Printf("Build Time: %s\n", buildTime)
	fmt.Printf("Git Commit: %s\n", gitCommit)
	fmt.Println()

	// 加载配置
	cfg, err := loadConfiguration(*configPath)
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// 创建日志记录器
	log, err := createLogger(cfg)
	if err != nil {
		fmt.Printf("Failed to create logger: %v\n", err)
		os.Exit(1)
	}
	defer log.Sync()

	log.Info("Starting WebSocket Hub Server",
		zap.String("version", version),
		zap.String("node_id", cfg.Server.NodeID),
		zap.String("address", cfg.GetServerAddress()),
	)

	// 创建设备管理器
	deviceManager := device.NewDeviceManager(cfg.Device.SecretKey)

	// 创建服务中心
	hubInstance := hub.NewHub(cfg.Server.NodeID, deviceManager, log)

	// 创建Kafka客户端（如果启用）
	if cfg.IsKafkaEnabled() {
		kafkaClient, err := kafka.NewKafkaClient(
			cfg.Kafka.Brokers,
			cfg.Server.NodeID,
			cfg.Kafka.Topic,
			hubInstance, // hub实现了MessageHandler接口
			log,
		)
		if err != nil {
			log.Fatal("Failed to create Kafka client", zap.Error(err))
		}
		hubInstance.SetKafkaClient(kafkaClient)
		log.Info("Kafka client configured",
			zap.Strings("brokers", cfg.Kafka.Brokers),
			zap.String("topic", cfg.Kafka.Topic),
		)
	} else {
		log.Info("Kafka disabled, running in standalone mode")
	}

	// 启动服务中心
	if err := hubInstance.Start(); err != nil {
		log.Fatal("Failed to start hub", zap.Error(err))
	}

	// 创建HTTP API服务器
	apiServer := api.NewServer(hubInstance, log)

	// 启动设备清理协程
	go startDeviceCleanup(deviceManager, cfg.Device.CleanupInterval, cfg.Device.MaxAge, log)

	// 启动HTTP服务器
	go func() {
		log.Info("Starting HTTP server", zap.String("address", cfg.GetServerAddress()))
		if err := apiServer.Start(cfg.GetServerAddress()); err != nil {
			log.Fatal("Failed to start HTTP server", zap.Error(err))
		}
	}()

	// 等待中断信号
	waitForShutdown(hubInstance, log)
}

// loadConfiguration 加载配置
func loadConfiguration(configPath string) (*config.Config, error) {
	// 首先尝试从环境变量加载
	cfg := config.LoadConfigFromEnv()

	// 如果指定了配置文件，则从文件加载并合并
	if configPath != "" {
		fileCfg, err := config.LoadConfig(configPath)
		if err != nil {
			return nil, fmt.Errorf("failed to load config from file: %w", err)
		}
		
		// 简单合并：文件配置覆盖环境变量配置
		if fileCfg != nil {
			cfg = fileCfg
		}
	}

	return cfg, nil
}

// createLogger 创建日志记录器
func createLogger(cfg *config.Config) (*zap.Logger, error) {
	if cfg.Server.Debug {
		return logger.NewDevelopmentLogger()
	}
	return logger.NewLogger(&cfg.Log)
}

// startDeviceCleanup 启动设备清理协程
func startDeviceCleanup(deviceManager *device.DeviceManager, interval, maxAge time.Duration, log *zap.Logger) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	log.Info("Device cleanup started",
		zap.Duration("interval", interval),
		zap.Duration("max_age", maxAge),
	)

	for range ticker.C {
		cleaned := deviceManager.CleanupExpiredDevices(maxAge)
		if cleaned > 0 {
			log.Info("Cleaned up expired devices", zap.Int("count", cleaned))
		}
	}
}

// waitForShutdown 等待关闭信号
func waitForShutdown(hubInstance *hub.Hub, log *zap.Logger) {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	sig := <-sigChan
	log.Info("Received shutdown signal", zap.String("signal", sig.String()))

	// 创建关闭上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭
	log.Info("Shutting down server...")
	
	if err := hubInstance.Stop(); err != nil {
		log.Error("Error during hub shutdown", zap.Error(err))
	}

	select {
	case <-ctx.Done():
		log.Warn("Shutdown timeout exceeded")
	default:
		log.Info("Server shutdown completed")
	}
}
