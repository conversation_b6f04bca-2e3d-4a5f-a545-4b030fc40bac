# Changelog

All notable changes to the Qt WebSocket Hub Go SDK will be documented in this file.

## [2.0.0] - 2025-07-11

### 🚀 Major Changes

- **BREAKING**: Complete rewrite from WebSocket client to HTTP client
- **BREAKING**: Removed WebSocket connection management
- **BREAKING**: Removed event-driven architecture
- **BREAKING**: Changed API from connection-based to request-based

### ✨ New Features

- **HTTP API Client**: Simple HTTP-based message sending without WebSocket connections
- **Device Management**: Create and manage device IDs through HTTP API
- **Convenience Methods**: Added `SendString()` and `SendJSON()` methods
- **Distributed Messaging**: Support for distributed message sending
- **Health Checks**: Built-in server health checking
- **Server Statistics**: Get real-time server statistics
- **Debug Logging**: Optional debug logging for HTTP requests/responses
- **Comprehensive Testing**: Full test suite with mock HTTP servers

### 📦 API Changes

#### New Methods
- `CreateDevice(tags []string) (string, error)` - Create device with tags
- `SendString(msgType, target string, tags []string, message string) error` - Send string messages
- `SendJSON(msgType, target string, tags []string, obj interface{}) error` - Send JSON messages
- `SendUnicastDistributed()`, `SendBroadcastDistributed()`, `SendTagcastDistributed()` - Distributed messaging
- `HealthCheck() (*HealthResponse, error)` - Server health check
- `GetServerStats() (*StatsResponse, error)` - Get server statistics

#### Removed Methods
- `Connect()`, `Disconnect()` - No longer needed with HTTP client
- `IsConnected()`, `GetState()` - Connection state not applicable
- `Events()` - Event-driven architecture removed
- `GetDeviceID()` - Device ID now returned from `CreateDevice()`

#### Changed Methods
- `GetStats()` now returns `ClientStats` instead of `ConnectionStats`
- All send methods now work immediately without requiring connection

### 🛠️ Configuration Changes

#### New Configuration
```go
type ClientConfig struct {
    ServerURL string        // HTTP server URL
    Timeout   time.Duration // HTTP request timeout  
    UserAgent string        // User agent string
    Debug     bool          // Enable debug logging
}
```

#### Removed Configuration
- Connection timeouts, heartbeat intervals, reconnection settings
- WebSocket-specific configuration options

### 📚 Examples

- **Simple Example**: Basic HTTP client usage
- **Advanced Example**: Interactive chat client (command-line based)
- **Notification Service**: Complete notification system example
- **Monitoring Service**: Real-time metrics collection and sending

### 🧪 Testing

- **70%+ Code Coverage**: Comprehensive test suite
- **Mock HTTP Servers**: Realistic testing with httptest
- **Integration Tests**: Full end-to-end testing
- **Benchmark Tests**: Performance testing for message operations

### 📖 Documentation

- **Complete README**: Updated with HTTP client examples
- **API Documentation**: Full method documentation
- **Usage Guide**: Detailed usage examples and best practices
- **Error Handling**: Comprehensive error handling examples

### 🔧 Development Tools

- **Makefile**: Complete build, test, and example targets
- **GitHub Actions**: CI/CD pipeline configuration
- **Docker Support**: Containerized testing
- **Development Scripts**: Helper scripts for common tasks

### 🎯 Benefits of HTTP Client Approach

1. **Simplicity**: No connection management required
2. **Reliability**: HTTP is more reliable than WebSocket for one-way messaging
3. **Scalability**: Better for high-throughput, fire-and-forget messaging
4. **Developer Friendly**: Easier to debug and test
5. **Stateless**: No connection state to manage
6. **Performance**: Lower overhead for simple message sending

### 📋 Migration Guide

#### From v1.x (WebSocket) to v2.0 (HTTP)

**Before (v1.x):**
```go
config := qt_websocket_hub.DefaultConfig("ws://localhost:8080/ws")
client := qt_websocket_hub.NewClient(config)

if err := client.Connect(); err != nil {
    log.Fatal(err)
}
defer client.Disconnect()

client.SendBroadcast([]byte("Hello"))
```

**After (v2.0):**
```go
config := qt_websocket_hub.DefaultConfig("http://localhost:8080")
client := qt_websocket_hub.NewClient(config)

// Optional: Create device for tracking
deviceID, _ := client.CreateDevice([]string{"my-app"})

client.SendBroadcast([]byte("Hello"))
```

### 🔄 Compatibility

- **Go Version**: Requires Go 1.21+
- **Server Version**: Compatible with Qt WebSocket Hub v1.0+
- **Dependencies**: Minimal dependencies (only standard library)

### 🐛 Bug Fixes

- Fixed message encoding/decoding issues
- Improved error handling and reporting
- Better timeout handling
- More robust HTTP client configuration

### 📈 Performance Improvements

- Reduced memory usage (no persistent connections)
- Lower CPU usage (no background goroutines)
- Faster message sending (direct HTTP calls)
- Better resource cleanup

---

## [1.0.0] - 2025-07-10

### Initial Release

- WebSocket-based client implementation
- Binary protocol support
- Event-driven architecture
- Connection management with auto-reconnect
- Heartbeat mechanism
- Basic message sending (unicast, broadcast, tagcast)

---

**Note**: Version 2.0.0 represents a complete architectural change from WebSocket to HTTP client. While this is a breaking change, it provides significant benefits in terms of simplicity, reliability, and developer experience for most use cases.
