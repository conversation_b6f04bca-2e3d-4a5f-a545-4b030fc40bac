package qt_websocket_hub

import (
	"encoding/binary"
	"fmt"
	"time"
)

// NewMessage 创建新消息
func NewMessage(msgType MessageType, payload []byte) *Message {
	header := MessageHeader{
		Magic:      MessageMagic,
		Version:    MessageVersion,
		Type:       msgType,
		Flags:      0,
		Reserved:   0,
		PayloadLen: uint32(len(payload)),
		Timestamp:  uint32(time.Now().Unix()),
		Checksum:   0,
	}

	msg := &Message{
		Header:  header,
		Payload: payload,
	}

	// 计算校验和
	msg.Header.Checksum = calculateChecksum(msg)
	return msg
}

// Marshal 序列化消息为二进制数据
func (m *Message) Marshal() ([]byte, error) {
	data := make([]byte, HeaderSize+len(m.Payload))

	// 写入头部
	binary.BigEndian.PutUint16(data[0:2], m.Header.Magic)
	data[2] = m.Header.Version
	data[3] = uint8(m.Header.Type)
	data[4] = m.Header.Flags
	data[5] = m.Header.Reserved
	binary.BigEndian.PutUint32(data[6:10], m.Header.PayloadLen)
	binary.BigEndian.PutUint32(data[10:14], m.Header.Timestamp)
	binary.BigEndian.PutUint16(data[14:16], m.Header.Checksum)

	// 写入载荷
	copy(data[HeaderSize:], m.Payload)

	return data, nil
}

// Unmarshal 从二进制数据反序列化消息
func UnmarshalMessage(data []byte) (*Message, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short for message header")
	}

	// 读取头部
	header := MessageHeader{
		Magic:      binary.BigEndian.Uint16(data[0:2]),
		Version:    data[2],
		Type:       MessageType(data[3]),
		Flags:      data[4],
		Reserved:   data[5],
		PayloadLen: binary.BigEndian.Uint32(data[6:10]),
		Timestamp:  binary.BigEndian.Uint32(data[10:14]),
		Checksum:   binary.BigEndian.Uint16(data[14:16]),
	}

	// 验证魔数和版本
	if header.Magic != MessageMagic {
		return nil, fmt.Errorf("invalid message magic: 0x%04x", header.Magic)
	}
	if header.Version != MessageVersion {
		return nil, fmt.Errorf("unsupported message version: %d", header.Version)
	}

	// 验证载荷长度
	if uint32(len(data)-HeaderSize) != header.PayloadLen {
		return nil, fmt.Errorf("payload length mismatch: expected %d, got %d",
			header.PayloadLen, len(data)-HeaderSize)
	}

	// 读取载荷
	payload := make([]byte, header.PayloadLen)
	copy(payload, data[HeaderSize:HeaderSize+header.PayloadLen])

	msg := &Message{
		Header:  header,
		Payload: payload,
	}

	// 验证校验和
	expectedChecksum := calculateChecksum(msg)
	if expectedChecksum != header.Checksum {
		return nil, fmt.Errorf("checksum verification failed: expected 0x%04x, got 0x%04x",
			expectedChecksum, header.Checksum)
	}

	return msg, nil
}

// calculateChecksum 计算消息校验和
func calculateChecksum(msg *Message) uint16 {
	var sum uint32

	// 计算头部校验和（除了校验和字段本身）
	sum += uint32(msg.Header.Magic)
	sum += uint32(msg.Header.Version)
	sum += uint32(msg.Header.Type)
	sum += uint32(msg.Header.Flags)
	sum += uint32(msg.Header.Reserved)
	sum += msg.Header.PayloadLen & 0xFFFF
	sum += (msg.Header.PayloadLen >> 16) & 0xFFFF
	sum += msg.Header.Timestamp & 0xFFFF
	sum += (msg.Header.Timestamp >> 16) & 0xFFFF

	// 计算载荷校验和
	for _, b := range msg.Payload {
		sum += uint32(b)
	}

	return uint16(sum & 0xFFFF)
}

// IsValid 验证消息是否有效
func (m *Message) IsValid() bool {
	return m.Header.Magic == MessageMagic &&
		m.Header.Version == MessageVersion &&
		uint32(len(m.Payload)) == m.Header.PayloadLen &&
		calculateChecksum(m) == m.Header.Checksum
}

// SetFlag 设置消息标志位
func (m *Message) SetFlag(flag uint8) {
	m.Header.Flags |= flag
	m.Header.Checksum = calculateChecksum(m)
}

// HasFlag 检查消息是否有指定标志位
func (m *Message) HasFlag(flag uint8) bool {
	return (m.Header.Flags & flag) != 0
}

// GetType 获取消息类型
func (m *Message) GetType() MessageType {
	return m.Header.Type
}

// GetPayload 获取消息载荷
func (m *Message) GetPayload() []byte {
	return m.Payload
}

// GetTimestamp 获取消息时间戳
func (m *Message) GetTimestamp() time.Time {
	return time.Unix(int64(m.Header.Timestamp), 0)
}
