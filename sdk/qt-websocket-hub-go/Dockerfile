# Qt WebSocket Hub Go SDK - Test Dockerfile

FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git make

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build examples
RUN make examples

# Test stage
FROM golang:1.21-alpine AS test

# Install test dependencies
RUN apk add --no-cache git make

WORKDIR /app

# Copy source
COPY . .

# Download dependencies
RUN go mod download

# Default command runs tests
CMD ["make", "test"]

# Runtime stage for examples
FROM alpine:latest AS runtime

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates

WORKDIR /root/

# Copy built examples
COPY --from=builder /app/build/* ./

# Default command
CMD ["./simple-example"]
