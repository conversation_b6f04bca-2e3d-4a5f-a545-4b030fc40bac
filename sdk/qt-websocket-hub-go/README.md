# Qt WebSocket Hub Go SDK

A Go client SDK for Qt WebSocket Hub with binary protocol support. This package allows Go applications to easily connect to and communicate with Qt WebSocket Hub servers.

## Features

- 🚀 **Binary Protocol**: Efficient binary message protocol, no JSON double parsing
- 🔐 **Secure**: Server-generated device IDs prevent malicious connections  
- 🏷️ **Tag System**: Support for connection tags and tag-based message routing
- 📦 **Simple API**: Easy-to-use client interface
- 🔄 **Auto Reconnect**: Automatic reconnection with configurable retry logic
- 💓 **Heartbeat**: Built-in heartbeat mechanism for connection health
- 🧪 **Well Tested**: Comprehensive test suite
- 📝 **Full Documentation**: Complete API documentation and examples

## Installation

```bash
go get github.com/your-username/qt-websocket-hub-go
```

## Quick Start

### Basic Usage

```go
package main

import (
    "fmt"
    "log"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
    // Create client configuration
    config := qt_websocket_hub.DefaultConfig("ws://localhost:8080/api/v1/ws")
    config.Tags = []string{"chat", "user"}
    config.ClientInfo = "My Go App v1.0.0"

    // Create client
    client := qt_websocket_hub.NewClient(config)

    // Connect to server
    if err := client.Connect(); err != nil {
        log.Fatalf("Failed to connect: %v", err)
    }
    defer client.Disconnect()

    // Send messages
    client.SendBroadcast([]byte("Hello, everyone!"))
    client.SendTagcast([]string{"admin"}, []byte("Admin message"))
    client.SendUnicast("target-device-id", []byte("Private message"))

    fmt.Printf("Connected with device ID: %s\n", client.GetDeviceID())
}
```

### Event Handling

```go
// Handle events
go func() {
    for event := range client.Events() {
        switch event.Type {
        case qt_websocket_hub.EventConnected:
            fmt.Printf("Connected: %v\n", event.Data)
            
        case qt_websocket_hub.EventMessageReceived:
            if msgEvent, ok := event.Data.(qt_websocket_hub.MessageEvent); ok {
                fmt.Printf("Message: %s\n", string(msgEvent.Payload))
            }
            
        case qt_websocket_hub.EventError:
            fmt.Printf("Error: %v\n", event.Error)
        }
    }
}()
```

## API Reference

### Client Configuration

```go
type ClientConfig struct {
    ServerURL            string        // WebSocket server URL
    Tags                 []string      // Connection tags
    DeviceID             string        // Existing device ID (optional)
    ClientInfo           string        // Client information
    ConnectTimeout       time.Duration // Connection timeout
    WriteTimeout         time.Duration // Write timeout
    HeartbeatInterval    time.Duration // Heartbeat interval
    AutoReconnect        bool          // Enable auto-reconnect
    ReconnectInterval    time.Duration // Reconnect interval
    MaxReconnectAttempts int           // Max reconnect attempts
}
```

### Client Methods

#### Connection Management

- `Connect() error` - Connect to WebSocket server
- `Disconnect() error` - Disconnect from server
- `IsConnected() bool` - Check if connected
- `GetState() ConnectionState` - Get current connection state
- `GetDeviceID() string` - Get assigned device ID

#### Message Sending

- `SendUnicast(targetDeviceID string, data []byte) error` - Send message to specific device
- `SendBroadcast(data []byte) error` - Send message to all connected devices
- `SendTagcast(tags []string, data []byte) error` - Send message to devices with specific tags

#### Information

- `GetStats() ConnectionStats` - Get connection statistics
- `Events() <-chan Event` - Get event channel for handling events

### Message Types

- **Unicast**: Point-to-point messaging to a specific device
- **Broadcast**: Message sent to all connected devices
- **Tagcast**: Message sent to devices with specific tags

### Connection States

- `StateDisconnected` - Not connected
- `StateConnecting` - Attempting to connect
- `StateConnected` - Successfully connected
- `StateReconnecting` - Attempting to reconnect
- `StateError` - Connection error

### Event Types

- `EventConnected` - Connection established
- `EventDisconnected` - Connection lost
- `EventError` - Error occurred
- `EventMessageReceived` - Message received from server

## Examples

### Simple Client

```go
package main

import (
    "fmt"
    "time"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
    config := qt_websocket_hub.DefaultConfig("ws://localhost:8080/api/v1/ws")
    config.Tags = []string{"example"}
    
    client := qt_websocket_hub.NewClient(config)
    
    if err := client.Connect(); err != nil {
        panic(err)
    }
    defer client.Disconnect()
    
    // Send a message every 5 seconds
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        client.SendBroadcast([]byte("Periodic message"))
    }
}
```

### Chat Application

```go
package main

import (
    "encoding/json"
    "fmt"
    "time"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

type ChatMessage struct {
    From    string    `json:"from"`
    Content string    `json:"content"`
    Time    time.Time `json:"time"`
}

func main() {
    config := qt_websocket_hub.DefaultConfig("ws://localhost:8080/api/v1/ws")
    config.Tags = []string{"chat", "general"}
    
    client := qt_websocket_hub.NewClient(config)
    
    // Handle incoming messages
    go func() {
        for event := range client.Events() {
            if event.Type == qt_websocket_hub.EventMessageReceived {
                if msgEvent, ok := event.Data.(qt_websocket_hub.MessageEvent); ok {
                    var chatMsg ChatMessage
                    if err := json.Unmarshal(msgEvent.Payload, &chatMsg); err == nil {
                        fmt.Printf("[%s] %s: %s\n", 
                            chatMsg.Time.Format("15:04:05"), 
                            chatMsg.From, 
                            chatMsg.Content)
                    }
                }
            }
        }
    }()
    
    if err := client.Connect(); err != nil {
        panic(err)
    }
    defer client.Disconnect()
    
    // Send a chat message
    msg := ChatMessage{
        From:    "GoClient",
        Content: "Hello from Go!",
        Time:    time.Now(),
    }
    
    data, _ := json.Marshal(msg)
    client.SendTagcast([]string{"general"}, data)
    
    // Keep running
    select {}
}
```

## Configuration Options

### Default Configuration

```go
config := qt_websocket_hub.DefaultConfig("ws://localhost:8080/api/v1/ws")
// Customize as needed
config.Tags = []string{"my-app", "production"}
config.ClientInfo = "My Application v2.0.0"
config.ConnectTimeout = 15 * time.Second
config.AutoReconnect = true
config.MaxReconnectAttempts = 5
```

### Custom Configuration

```go
config := &qt_websocket_hub.ClientConfig{
    ServerURL:            "ws://example.com:8080/api/v1/ws",
    Tags:                 []string{"custom", "app"},
    ClientInfo:           "Custom Client v1.0.0",
    ConnectTimeout:       10 * time.Second,
    WriteTimeout:         5 * time.Second,
    HeartbeatInterval:    30 * time.Second,
    AutoReconnect:        true,
    ReconnectInterval:    3 * time.Second,
    MaxReconnectAttempts: 10,
}
```

## Error Handling

```go
client := qt_websocket_hub.NewClient(config)

// Handle connection errors
if err := client.Connect(); err != nil {
    log.Printf("Connection failed: %v", err)
    return
}

// Handle runtime errors
go func() {
    for event := range client.Events() {
        if event.Type == qt_websocket_hub.EventError {
            log.Printf("Runtime error: %v", event.Error)
        }
    }
}()

// Handle send errors
if err := client.SendBroadcast([]byte("test")); err != nil {
    log.Printf("Send failed: %v", err)
}
```

## Testing

```bash
# Run tests
go test

# Run tests with coverage
go test -cover

# Run benchmarks
go test -bench=.
```

## Examples

Check out the [examples](examples/) directory for complete working examples:

- [Simple Client](examples/simple/) - Basic usage example
- [Advanced Chat](examples/advanced/) - Full-featured chat application

## Requirements

- Go 1.21 or later
- Qt WebSocket Hub server

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Support

- 🐛 [Report Issues](https://github.com/your-username/qt-websocket-hub-go/issues)
- 📚 [Documentation](https://pkg.go.dev/github.com/your-username/qt-websocket-hub-go)
- 💬 [Discussions](https://github.com/your-username/qt-websocket-hub-go/discussions)
