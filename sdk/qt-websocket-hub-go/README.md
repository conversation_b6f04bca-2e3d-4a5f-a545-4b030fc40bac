# Qt WebSocket Hub Go SDK

A Go client SDK for Qt WebSocket Hub using HTTP API. This package allows Go applications to easily send messages through Qt WebSocket Hub servers without maintaining WebSocket connections.

## Features

- 🌐 **HTTP API**: Simple HTTP-based message sending, no WebSocket connection required
- 🔐 **Secure**: Server-generated device IDs prevent malicious connections
- 🏷️ **Tag System**: Support for connection tags and tag-based message routing
- 📦 **Simple API**: Easy-to-use client interface with convenient methods
- ⚡ **High Performance**: Direct HTTP calls without connection overhead
- 🛠️ **Developer Friendly**: JSON and string convenience methods
- 🧪 **Well Tested**: Comprehensive test suite
- 📝 **Full Documentation**: Complete API documentation and examples

## Installation

```bash
go get github.com/your-username/qt-websocket-hub-go
```

## Quick Start

### Basic Usage

```go
package main

import (
    "fmt"
    "log"

    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
    // Create client configuration
    config := qt_websocket_hub.DefaultConfig("http://localhost:9090")
    config.Debug = true // Enable debug logging

    // Create client
    client := qt_websocket_hub.NewClient(config)

    // Create device ID (optional, for tracking)
    deviceID, err := client.CreateDevice([]string{"my-app", "production"})
    if err != nil {
        log.Fatalf("Failed to create device: %v", err)
    }
    fmt.Printf("Device ID: %s\n", deviceID)

    // Send messages
    client.SendBroadcast([]byte("Hello, everyone!"))
    client.SendTagcast([]string{"admin"}, []byte("Admin message"))
    client.SendUnicast("target-device-id", []byte("Private message"))

    // Use convenience methods
    client.SendString("broadcast", "", nil, "Hello from string method!")
    client.SendJSON("tagcast", "", []string{"users"}, map[string]string{
        "type": "notification",
        "message": "Hello from JSON method!",
    })
}
```

### Error Handling

```go
// Check for errors after sending messages
if err := client.SendBroadcast([]byte("Hello")); err != nil {
    log.Printf("Failed to send broadcast: %v", err)
}

// Get client statistics
stats := client.GetStats()
fmt.Printf("Messages sent: %d, Success rate: %.2f%%\n",
    stats.MessagesSent,
    float64(stats.RequestsSuccess)/float64(stats.RequestsTotal)*100)

// Health check
health, err := client.HealthCheck()
if err != nil {
    log.Printf("Health check failed: %v", err)
} else {
    fmt.Printf("Server status: %s\n", health.Status)
}
```

## API Reference

### Client Configuration

```go
type ClientConfig struct {
    ServerURL string        // HTTP server URL (e.g., "http://localhost:9090")
    Timeout   time.Duration // HTTP request timeout
    UserAgent string        // User agent string
    Debug     bool          // Enable debug logging
}
```

### Client Methods

#### Device Management

- `CreateDevice(tags []string) (string, error)` - Create a new device ID with tags

#### Message Sending

- `SendUnicast(targetDeviceID string, data []byte) error` - Send message to specific device
- `SendBroadcast(data []byte) error` - Send message to all connected devices
- `SendTagcast(tags []string, data []byte) error` - Send message to devices with specific tags
- `SendUnicastDistributed(targetDeviceID string, data []byte) error` - Send distributed unicast
- `SendBroadcastDistributed(data []byte) error` - Send distributed broadcast
- `SendTagcastDistributed(tags []string, data []byte) error` - Send distributed tagcast

#### Convenience Methods

- `SendString(msgType, target string, tags []string, message string) error` - Send string message
- `SendJSON(msgType, target string, tags []string, obj interface{}) error` - Send JSON message

#### Information

- `GetStats() ClientStats` - Get client statistics
- `GetServerStats() (*StatsResponse, error)` - Get server statistics
- `HealthCheck() (*HealthResponse, error)` - Perform health check

### Message Types

- **Unicast**: Point-to-point messaging to a specific device
- **Broadcast**: Message sent to all connected devices
- **Tagcast**: Message sent to devices with specific tags

### Response Types

- `HTTPResponse` - Standard HTTP response structure
- `DeviceCreateResponse` - Device creation response
- `StatsResponse` - Server statistics response
- `HealthResponse` - Health check response

## Examples

### Simple Client

```go
package main

import (
    "fmt"
    "log"
    "time"

    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
    config := qt_websocket_hub.DefaultConfig("http://localhost:9090")
    client := qt_websocket_hub.NewClient(config)

    // Create device
    deviceID, err := client.CreateDevice([]string{"example"})
    if err != nil {
        log.Fatalf("Failed to create device: %v", err)
    }
    fmt.Printf("Device ID: %s\n", deviceID)

    // Send periodic messages
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()

    for i := 0; i < 5; i++ {
        message := fmt.Sprintf("Periodic message #%d", i+1)
        if err := client.SendBroadcast([]byte(message)); err != nil {
            log.Printf("Failed to send message: %v", err)
        } else {
            fmt.Printf("Sent: %s\n", message)
        }
        <-ticker.C
    }
}
```

### Chat Application

```go
package main

import (
    "fmt"
    "log"
    "time"

    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

type ChatMessage struct {
    From    string    `json:"from"`
    Content string    `json:"content"`
    Time    time.Time `json:"time"`
    Room    string    `json:"room"`
}

func main() {
    config := qt_websocket_hub.DefaultConfig("http://localhost:9090")
    client := qt_websocket_hub.NewClient(config)

    // Create device for this chat client
    deviceID, err := client.CreateDevice([]string{"chat", "general"})
    if err != nil {
        log.Fatalf("Failed to create device: %v", err)
    }
    fmt.Printf("Chat client device ID: %s\n", deviceID)

    // Send join message
    joinMsg := ChatMessage{
        From:    "GoClient",
        Content: "joined the room",
        Time:    time.Now(),
        Room:    "general",
    }

    if err := client.SendJSON("tagcast", "", []string{"general"}, joinMsg); err != nil {
        log.Printf("Failed to send join message: %v", err)
    }

    // Send some chat messages
    messages := []string{
        "Hello everyone!",
        "How is everyone doing?",
        "This is a message from Go SDK",
    }

    for _, content := range messages {
        msg := ChatMessage{
            From:    "GoClient",
            Content: content,
            Time:    time.Now(),
            Room:    "general",
        }

        if err := client.SendJSON("tagcast", "", []string{"general"}, msg); err != nil {
            log.Printf("Failed to send message: %v", err)
        } else {
            fmt.Printf("Sent: %s\n", content)
        }

        time.Sleep(2 * time.Second)
    }

    // Send leave message
    leaveMsg := ChatMessage{
        From:    "GoClient",
        Content: "left the room",
        Time:    time.Now(),
        Room:    "general",
    }
    client.SendJSON("tagcast", "", []string{"general"}, leaveMsg)
}
```

## Configuration Options

### Default Configuration

```go
config := qt_websocket_hub.DefaultConfig("http://localhost:9090")
// Customize as needed
config.Timeout = 30 * time.Second
config.UserAgent = "My Application v2.0.0"
config.Debug = true // Enable debug logging
```

### Custom Configuration

```go
config := &qt_websocket_hub.ClientConfig{
    ServerURL: "http://example.com:8080",
    Timeout:   15 * time.Second,
    UserAgent: "Custom Client v1.0.0",
    Debug:     false,
}
```

## Error Handling

```go
client := qt_websocket_hub.NewClient(config)

// Handle device creation errors
deviceID, err := client.CreateDevice([]string{"my-app"})
if err != nil {
    log.Printf("Device creation failed: %v", err)
    return
}

// Handle send errors
if err := client.SendBroadcast([]byte("test")); err != nil {
    log.Printf("Send failed: %v", err)
}

// Check client statistics for error rates
stats := client.GetStats()
if stats.RequestsFailed > 0 {
    errorRate := float64(stats.RequestsFailed) / float64(stats.RequestsTotal) * 100
    log.Printf("Error rate: %.2f%% (%d/%d)", errorRate, stats.RequestsFailed, stats.RequestsTotal)
    if stats.LastError != nil {
        log.Printf("Last error: %v", stats.LastError)
    }
}

// Health check
if health, err := client.HealthCheck(); err != nil {
    log.Printf("Health check failed: %v", err)
} else if health.Status != "healthy" {
    log.Printf("Server not healthy: %s", health.Status)
}
```

## Testing

```bash
# Run tests
go test

# Run tests with coverage
go test -cover

# Run benchmarks
go test -bench=.
```

## Examples

Check out the [examples](examples/) directory for complete working examples:

- [Simple Client](examples/simple/) - Basic usage example
- [Advanced Chat](examples/advanced/) - Full-featured chat application

## Requirements

- Go 1.21 or later
- Qt WebSocket Hub server

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Support

- 🐛 [Report Issues](https://github.com/your-username/qt-websocket-hub-go/issues)
- 📚 [Documentation](https://pkg.go.dev/github.com/your-username/qt-websocket-hub-go)
- 💬 [Discussions](https://github.com/your-username/qt-websocket-hub-go/discussions)
