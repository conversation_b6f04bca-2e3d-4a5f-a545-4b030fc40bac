package qt_websocket_hub

import (
	"testing"
	"time"
)

func TestNewMessage(t *testing.T) {
	payload := []byte("Hello, World!")
	msg := NewMessage(MsgTypeUnicast, payload)

	if msg.Header.Magic != MessageMagic {
		t.<PERSON>("Expected magic %x, got %x", MessageMagic, msg.Header.Magic)
	}

	if msg.Header.Version != MessageVersion {
		t.<PERSON>("Expected version %d, got %d", MessageVersion, msg.Header.Version)
	}

	if msg.Header.Type != MsgTypeUnicast {
		t.<PERSON>rf("Expected type %v, got %v", MsgTypeUnicast, msg.Header.Type)
	}

	if msg.Header.PayloadLen != uint32(len(payload)) {
		t.<PERSON>rf("Expected payload length %d, got %d", len(payload), msg.Header.PayloadLen)
	}

	if string(msg.Payload) != string(payload) {
		t.<PERSON>("Expected payload %s, got %s", string(payload), string(msg.Payload))
	}

	if !msg.IsValid() {
		t.<PERSON>r("Message should be valid")
	}
}

func TestMessageMarshalUnmarshal(t *testing.T) {
	testPayload := []byte("Test message for marshal/unmarshal")
	originalMsg := NewMessage(MsgTypeBroadcast, testPayload)

	// Marshal
	data, err := originalMsg.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal message: %v", err)
	}

	if len(data) != HeaderSize+len(testPayload) {
		t.Errorf("Expected data length %d, got %d", HeaderSize+len(testPayload), len(data))
	}

	// Unmarshal
	unmarshaledMsg, err := UnmarshalMessage(data)
	if err != nil {
		t.Fatalf("Failed to unmarshal message: %v", err)
	}

	// Verify
	if unmarshaledMsg.Header.Magic != originalMsg.Header.Magic {
		t.Errorf("Magic mismatch: expected %x, got %x", originalMsg.Header.Magic, unmarshaledMsg.Header.Magic)
	}

	if unmarshaledMsg.Header.Type != originalMsg.Header.Type {
		t.Errorf("Type mismatch: expected %v, got %v", originalMsg.Header.Type, unmarshaledMsg.Header.Type)
	}

	if unmarshaledMsg.Header.PayloadLen != originalMsg.Header.PayloadLen {
		t.Errorf("PayloadLen mismatch: expected %d, got %d", originalMsg.Header.PayloadLen, unmarshaledMsg.Header.PayloadLen)
	}

	if string(unmarshaledMsg.Payload) != string(originalMsg.Payload) {
		t.Errorf("Payload mismatch: expected %s, got %s", string(originalMsg.Payload), string(unmarshaledMsg.Payload))
	}

	if !unmarshaledMsg.IsValid() {
		t.Error("Unmarshaled message should be valid")
	}
}

func TestMessageTypes(t *testing.T) {
	testCases := []struct {
		msgType MessageType
		name    string
	}{
		{MsgTypeHandshake, "Handshake"},
		{MsgTypeHandshakeResp, "HandshakeResp"},
		{MsgTypeHeartbeat, "Heartbeat"},
		{MsgTypeDisconnect, "Disconnect"},
		{MsgTypeUnicast, "Unicast"},
		{MsgTypeBroadcast, "Broadcast"},
		{MsgTypeTagcast, "Tagcast"},
		{MsgTypeError, "Error"},
		{MsgTypeAck, "Ack"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			msg := NewMessage(tc.msgType, []byte("test"))

			if msg.GetType() != tc.msgType {
				t.Errorf("Expected type %v, got %v", tc.msgType, msg.GetType())
			}

			data, err := msg.Marshal()
			if err != nil {
				t.Fatalf("Failed to marshal %s message: %v", tc.name, err)
			}

			newMsg, err := UnmarshalMessage(data)
			if err != nil {
				t.Fatalf("Failed to unmarshal %s message: %v", tc.name, err)
			}

			if newMsg.GetType() != tc.msgType {
				t.Errorf("After unmarshal, expected type %v, got %v", tc.msgType, newMsg.GetType())
			}
		})
	}
}

func TestMessageFlags(t *testing.T) {
	msg := NewMessage(MsgTypeUnicast, []byte("test"))

	// Test compressed flag
	msg.SetFlag(FlagCompressed)
	if !msg.HasFlag(FlagCompressed) {
		t.Error("Message should have compressed flag")
	}

	// Test encrypted flag
	msg.SetFlag(FlagEncrypted)
	if !msg.HasFlag(FlagEncrypted) {
		t.Error("Message should have encrypted flag")
	}

	// Test reliable flag
	msg.SetFlag(FlagReliable)
	if !msg.HasFlag(FlagReliable) {
		t.Error("Message should have reliable flag")
	}

	// Verify all flags are set
	expectedFlags := uint8(FlagCompressed | FlagEncrypted | FlagReliable)
	if msg.Header.Flags != expectedFlags {
		t.Errorf("Expected flags %d, got %d", expectedFlags, msg.Header.Flags)
	}
}

func TestMessageValidation(t *testing.T) {
	// Test valid message
	validMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	if !validMsg.IsValid() {
		t.Error("Valid message should pass validation")
	}

	// Test invalid magic
	invalidMagicMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	invalidMagicMsg.Header.Magic = 0x1234
	if invalidMagicMsg.IsValid() {
		t.Error("Message with invalid magic should fail validation")
	}

	// Test invalid version
	invalidVersionMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	invalidVersionMsg.Header.Version = 0xFF
	if invalidVersionMsg.IsValid() {
		t.Error("Message with invalid version should fail validation")
	}

	// Test payload length mismatch
	invalidLengthMsg := NewMessage(MsgTypeUnicast, []byte("test"))
	invalidLengthMsg.Header.PayloadLen = 100
	if invalidLengthMsg.IsValid() {
		t.Error("Message with invalid payload length should fail validation")
	}
}

func TestMessageChecksum(t *testing.T) {
	msg := NewMessage(MsgTypeUnicast, []byte("test"))
	originalChecksum := msg.Header.Checksum

	// Modify payload
	msg.Payload = []byte("modified")

	// Checksum should not match
	if msg.IsValid() {
		t.Error("Message with modified payload should fail checksum validation")
	}

	// Recalculate checksum
	msg.Header.PayloadLen = uint32(len(msg.Payload))
	msg.Header.Checksum = calculateChecksum(msg)

	// Should be valid now
	if !msg.IsValid() {
		t.Error("Message with recalculated checksum should be valid")
	}

	// Checksum should be different
	if msg.Header.Checksum == originalChecksum {
		t.Error("Checksum should be different after payload modification")
	}
}

func TestEmptyPayload(t *testing.T) {
	// Test empty payload
	msg := NewMessage(MsgTypeHeartbeat, nil)

	if msg.Header.PayloadLen != 0 {
		t.Errorf("Expected payload length 0, got %d", msg.Header.PayloadLen)
	}

	if len(msg.Payload) != 0 {
		t.Errorf("Expected empty payload, got length %d", len(msg.Payload))
	}

	if !msg.IsValid() {
		t.Error("Message with empty payload should be valid")
	}

	// Marshal and unmarshal
	data, err := msg.Marshal()
	if err != nil {
		t.Fatalf("Failed to marshal message with empty payload: %v", err)
	}

	newMsg, err := UnmarshalMessage(data)
	if err != nil {
		t.Fatalf("Failed to unmarshal message with empty payload: %v", err)
	}

	if !newMsg.IsValid() {
		t.Error("Unmarshaled message with empty payload should be valid")
	}
}

func TestMessageTimestamp(t *testing.T) {
	before := time.Now().Unix()
	msg := NewMessage(MsgTypeUnicast, []byte("test"))
	after := time.Now().Unix()

	timestamp := msg.GetTimestamp().Unix()

	if timestamp < before || timestamp > after {
		t.Errorf("Timestamp %d should be between %d and %d", timestamp, before, after)
	}
}

func BenchmarkMessageMarshal(b *testing.B) {
	payload := make([]byte, 1024) // 1KB payload
	msg := NewMessage(MsgTypeUnicast, payload)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := msg.Marshal()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkMessageUnmarshal(b *testing.B) {
	payload := make([]byte, 1024) // 1KB payload
	msg := NewMessage(MsgTypeUnicast, payload)
	data, _ := msg.Marshal()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := UnmarshalMessage(data)
		if err != nil {
			b.Fatal(err)
		}
	}
}
