package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

// ChatMessage 聊天消息结构
type ChatMessage struct {
	From      string    `json:"from"`
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
	Type      string    `json:"type"` // "chat", "system", "notification"
}

// NotificationMessage 通知消息结构
type NotificationMessage struct {
	Title   string    `json:"title"`
	Content string    `json:"content"`
	Level   string    `json:"level"` // "info", "warning", "error"
	Time    time.Time `json:"time"`
}

// ChatClient 聊天客户端
type ChatClient struct {
	client   *qt_websocket_hub.Client
	username string
	room     string
}

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: go run main.go <username> <room>")
		fmt.Println("Example: go run main.go alice general")
		os.Exit(1)
	}

	username := os.Args[1]
	room := os.Args[2]

	// 创建聊天客户端
	chatClient := NewChatClient(username, room)

	// 连接到服务器
	fmt.Printf("Connecting as %s to room %s...\n", username, room)
	if err := chatClient.Connect(); err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}

	// 等待连接建立
	time.Sleep(2 * time.Second)

	if !chatClient.IsConnected() {
		fmt.Println("Failed to establish connection")
		return
	}

	fmt.Printf("Connected! Device ID: %s\n", chatClient.GetDeviceID())
	fmt.Println("Type messages and press Enter. Type 'quit' to exit.")

	// 启动消息处理
	go chatClient.handleMessages()

	// 发送加入房间通知
	chatClient.SendSystemMessage(fmt.Sprintf("%s joined the room", username))

	// 处理用户输入
	go chatClient.handleUserInput()

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	// 发送离开房间通知
	chatClient.SendSystemMessage(fmt.Sprintf("%s left the room", username))

	// 断开连接
	fmt.Println("\nDisconnecting...")
	chatClient.Disconnect()
	fmt.Println("Goodbye!")
}

func NewChatClient(username, room string) *ChatClient {
	config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
	config.Tags = []string{"chat", room, "user"}
	config.ClientInfo = fmt.Sprintf("Go Chat Client - %s", username)

	client := qt_websocket_hub.NewClient(config)

	return &ChatClient{
		client:   client,
		username: username,
		room:     room,
	}
}

func (cc *ChatClient) Connect() error {
	return cc.client.Connect()
}

func (cc *ChatClient) Disconnect() error {
	return cc.client.Disconnect()
}

func (cc *ChatClient) IsConnected() bool {
	return cc.client.IsConnected()
}

func (cc *ChatClient) GetDeviceID() string {
	return cc.client.GetDeviceID()
}

func (cc *ChatClient) SendChatMessage(content string) error {
	msg := ChatMessage{
		From:      cc.username,
		Content:   content,
		Timestamp: time.Now(),
		Type:      "chat",
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal chat message: %w", err)
	}

	// 发送到房间（使用标签组播）
	return cc.client.SendTagcast([]string{cc.room}, data)
}

func (cc *ChatClient) SendSystemMessage(content string) error {
	msg := ChatMessage{
		From:      "System",
		Content:   content,
		Timestamp: time.Now(),
		Type:      "system",
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal system message: %w", err)
	}

	return cc.client.SendTagcast([]string{cc.room}, data)
}

func (cc *ChatClient) SendNotification(title, content, level string) error {
	msg := NotificationMessage{
		Title:   title,
		Content: content,
		Level:   level,
		Time:    time.Now(),
	}

	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	// 发送通知到所有用户
	return cc.client.SendTagcast([]string{"user"}, data)
}

func (cc *ChatClient) handleMessages() {
	for event := range cc.client.Events() {
		switch event.Type {
		case qt_websocket_hub.EventConnected:
			if deviceID, ok := event.Data.(string); ok {
				fmt.Printf("✓ Connected with device ID: %s\n", deviceID)
			}

		case qt_websocket_hub.EventDisconnected:
			fmt.Printf("✗ Disconnected: %v\n", event.Data)

		case qt_websocket_hub.EventError:
			fmt.Printf("✗ Error: %v\n", event.Error)

		case qt_websocket_hub.EventMessageReceived:
			if msgEvent, ok := event.Data.(qt_websocket_hub.MessageEvent); ok {
				cc.processMessage(msgEvent)
			}
		}
	}
}

func (cc *ChatClient) processMessage(msgEvent qt_websocket_hub.MessageEvent) {
	switch msgEvent.Type {
	case qt_websocket_hub.MsgTypeBroadcast, qt_websocket_hub.MsgTypeTagcast:
		// 尝试解析为聊天消息
		var chatMsg ChatMessage
		if err := json.Unmarshal(msgEvent.Payload, &chatMsg); err == nil {
			cc.displayChatMessage(chatMsg)
			return
		}

		// 尝试解析为通知消息
		var notifMsg NotificationMessage
		if err := json.Unmarshal(msgEvent.Payload, &notifMsg); err == nil {
			cc.displayNotification(notifMsg)
			return
		}

		// 如果都不是，显示原始消息
		fmt.Printf("[RAW] %s\n", string(msgEvent.Payload))

	case qt_websocket_hub.MsgTypeUnicast:
		fmt.Printf("[PRIVATE] %s\n", string(msgEvent.Payload))
	}
}

func (cc *ChatClient) displayChatMessage(msg ChatMessage) {
	timeStr := msg.Timestamp.Format("15:04:05")

	switch msg.Type {
	case "chat":
		if msg.From == cc.username {
			fmt.Printf("[%s] You: %s\n", timeStr, msg.Content)
		} else {
			fmt.Printf("[%s] %s: %s\n", timeStr, msg.From, msg.Content)
		}
	case "system":
		fmt.Printf("[%s] * %s\n", timeStr, msg.Content)
	default:
		fmt.Printf("[%s] [%s] %s: %s\n", timeStr, msg.Type, msg.From, msg.Content)
	}
}

func (cc *ChatClient) displayNotification(msg NotificationMessage) {
	timeStr := msg.Time.Format("15:04:05")
	levelIcon := "ℹ"

	switch msg.Level {
	case "warning":
		levelIcon = "⚠"
	case "error":
		levelIcon = "✗"
	}

	fmt.Printf("[%s] %s %s: %s\n", timeStr, levelIcon, msg.Title, msg.Content)
}

func (cc *ChatClient) handleUserInput() {
	var input string
	for {
		fmt.Print("> ")
		if _, err := fmt.Scanln(&input); err != nil {
			continue
		}

		if input == "quit" || input == "exit" {
			// 发送退出信号
			p, _ := os.FindProcess(os.Getpid())
			p.Signal(syscall.SIGTERM)
			return
		}

		if input == "" {
			continue
		}

		// 特殊命令处理
		if input == "/stats" {
			stats := cc.client.GetStats()
			fmt.Printf("Stats: Sent=%d, Received=%d, Connected=%v\n",
				stats.MessagesSent, stats.MessagesReceived,
				stats.ConnectedAt.Format("15:04:05"))
			continue
		}

		if input == "/notify" {
			cc.SendNotification("Test", "This is a test notification", "info")
			continue
		}

		// 发送聊天消息
		if err := cc.SendChatMessage(input); err != nil {
			fmt.Printf("Failed to send message: %v\n", err)
		}
	}
}
