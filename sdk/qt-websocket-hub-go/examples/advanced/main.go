package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

// ChatMessage 聊天消息结构
type ChatMessage struct {
	From      string    `json:"from"`
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
	Type      string    `json:"type"` // "chat", "system", "notification"
}

// NotificationMessage 通知消息结构
type NotificationMessage struct {
	Title   string    `json:"title"`
	Content string    `json:"content"`
	Level   string    `json:"level"` // "info", "warning", "error"
	Time    time.Time `json:"time"`
}

// ChatClient 聊天客户端
type ChatClient struct {
	client   *qt_websocket_hub.Client
	username string
	room     string
	deviceID string
}

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: go run main.go <username> <room>")
		fmt.Println("Example: go run main.go alice general")
		os.Exit(1)
	}

	username := os.Args[1]
	room := os.Args[2]

	// 创建聊天客户端
	chatClient := NewChatClient(username, room)

	// 初始化客户端
	fmt.Printf("Initializing chat client for %s in room %s...\n", username, room)
	if err := chatClient.Initialize(); err != nil {
		log.Fatalf("Failed to initialize: %v", err)
	}

	fmt.Printf("Initialized! Device ID: %s\n", chatClient.GetDeviceID())
	fmt.Println("Type messages and press Enter. Type 'quit' to exit.")
	fmt.Println("Commands: /stats, /notify, /help")

	// 发送加入房间通知
	chatClient.SendSystemMessage(fmt.Sprintf("%s joined the room", username))

	// 处理用户输入
	chatClient.handleUserInput()

	// 发送离开房间通知
	chatClient.SendSystemMessage(fmt.Sprintf("%s left the room", username))

	fmt.Println("Goodbye!")
}

func NewChatClient(username, room string) *ChatClient {
	config := qt_websocket_hub.DefaultConfig("http://localhost:9090")
	config.Debug = false // 可以设置为true来查看HTTP请求

	client := qt_websocket_hub.NewClient(config)

	return &ChatClient{
		client:   client,
		username: username,
		room:     room,
	}
}

func (cc *ChatClient) Initialize() error {
	// 创建设备ID
	deviceID, err := cc.client.CreateDevice([]string{"chat", cc.room, "user"})
	if err != nil {
		return fmt.Errorf("failed to create device: %w", err)
	}
	cc.deviceID = deviceID
	return nil
}

func (cc *ChatClient) GetDeviceID() string {
	return cc.deviceID
}

func (cc *ChatClient) SendChatMessage(content string) error {
	msg := ChatMessage{
		From:      cc.username,
		Content:   content,
		Timestamp: time.Now(),
		Type:      "chat",
	}

	// 使用JSON便捷方法发送到房间
	return cc.client.SendJSON("tagcast", "", []string{cc.room}, msg)
}

func (cc *ChatClient) SendSystemMessage(content string) error {
	msg := ChatMessage{
		From:      "System",
		Content:   content,
		Timestamp: time.Now(),
		Type:      "system",
	}

	return cc.client.SendJSON("tagcast", "", []string{cc.room}, msg)
}

func (cc *ChatClient) SendNotification(title, content, level string) error {
	msg := NotificationMessage{
		Title:   title,
		Content: content,
		Level:   level,
		Time:    time.Now(),
	}

	// 发送通知到所有用户
	return cc.client.SendJSON("tagcast", "", []string{"user"}, msg)
}

func (cc *ChatClient) handleUserInput() {
	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Print("> ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "" {
			continue
		}

		if input == "quit" || input == "exit" {
			break
		}

		// 处理命令
		if strings.HasPrefix(input, "/") {
			cc.handleCommand(input)
			continue
		}

		// 发送聊天消息
		if err := cc.SendChatMessage(input); err != nil {
			fmt.Printf("Failed to send message: %v\n", err)
		} else {
			fmt.Printf("[%s] You: %s\n", time.Now().Format("15:04:05"), input)
		}
	}
}

func (cc *ChatClient) handleCommand(input string) {
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return
	}

	command := parts[0]
	switch command {
	case "/stats":
		cc.showStats()
	case "/notify":
		cc.SendNotification("Test", "This is a test notification", "info")
		fmt.Println("Test notification sent")
	case "/help":
		cc.showHelp()
	default:
		fmt.Printf("Unknown command: %s. Type /help for available commands.\n", command)
	}
}

func (cc *ChatClient) showStats() {
	fmt.Println("=== Statistics ===")

	// 客户端统计
	clientStats := cc.client.GetStats()
	fmt.Printf("Client - Messages Sent: %d, Requests: %d/%d (success/total)\n",
		clientStats.MessagesSent, clientStats.RequestsSuccess, clientStats.RequestsTotal)

	// 服务器统计
	if serverStats, err := cc.client.GetServerStats(); err == nil {
		fmt.Printf("Server - Connections: %d, Messages: %d sent / %d received\n",
			serverStats.CurrentConnections, serverStats.MessagesSent, serverStats.MessagesReceived)
	}
}

func (cc *ChatClient) showHelp() {
	fmt.Println("=== Available Commands ===")
	fmt.Println("/stats  - Show statistics")
	fmt.Println("/notify - Send test notification")
	fmt.Println("/help   - Show this help")
	fmt.Println("quit    - Exit the chat")
}
