package main

import (
	"flag"
	"fmt"
	"log"
	"math/rand"
	"time"

	qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

// MonitoringService 监控服务
type MonitoringService struct {
	client   *qt_websocket_hub.Client
	deviceID string
	name     string
}

// MetricData 监控指标数据
type MetricData struct {
	Service   string            `json:"service"`
	Host      string            `json:"host"`
	Metric    string            `json:"metric"`
	Value     float64           `json:"value"`
	Unit      string            `json:"unit"`
	Timestamp time.Time         `json:"timestamp"`
	Tags      map[string]string `json:"tags"`
	Level     string            `json:"level"` // normal, warning, critical
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	NetworkIn   float64 `json:"network_in"`
	NetworkOut  float64 `json:"network_out"`
}

func main() {
	var (
		serverURL = flag.String("server", "http://localhost:9090", "WebSocket Hub server URL")
		debug     = flag.Bool("debug", false, "Enable debug logging")
		name      = flag.String("name", "monitoring-service", "Service name")
		interval  = flag.Duration("interval", 10*time.Second, "Monitoring interval")
		duration  = flag.Duration("duration", 2*time.Minute, "Demo duration")
	)
	flag.Parse()

	fmt.Printf("Qt WebSocket Hub - Monitoring Service\n")
	fmt.Printf("====================================\n")
	fmt.Printf("Server: %s\n", *serverURL)
	fmt.Printf("Service: %s\n", *name)
	fmt.Printf("Interval: %v\n", *interval)
	fmt.Printf("Duration: %v\n", *duration)
	fmt.Printf("Debug: %v\n\n", *debug)

	// 创建监控服务
	service, err := NewMonitoringService(*serverURL, *name, *debug)
	if err != nil {
		log.Fatalf("Failed to create monitoring service: %v", err)
	}

	fmt.Printf("Monitoring service started with device ID: %s\n\n", service.deviceID)

	// 运行监控演示
	service.RunDemo(*interval, *duration)

	// 显示统计信息
	service.ShowStats()
}

// NewMonitoringService 创建新的监控服务
func NewMonitoringService(serverURL, name string, debug bool) (*MonitoringService, error) {
	config := qt_websocket_hub.DefaultConfig(serverURL)
	config.Debug = debug
	config.UserAgent = fmt.Sprintf("MonitoringService/%s v1.0.0", name)

	client := qt_websocket_hub.NewClient(config)

	// 健康检查
	health, err := client.HealthCheck()
	if err != nil {
		return nil, fmt.Errorf("health check failed: %w", err)
	}

	if health.Status != "healthy" {
		return nil, fmt.Errorf("server not healthy: %s", health.Status)
	}

	// 创建设备ID
	deviceID, err := client.CreateDevice([]string{"monitoring", "metrics", name})
	if err != nil {
		return nil, fmt.Errorf("failed to create device: %w", err)
	}

	return &MonitoringService{
		client:   client,
		deviceID: deviceID,
		name:     name,
	}, nil
}

// SendMetric 发送单个指标
func (ms *MonitoringService) SendMetric(metric MetricData) error {
	metric.Timestamp = time.Now()

	// 根据指标级别选择目标
	var targets []string
	switch metric.Level {
	case "critical":
		targets = []string{"monitoring", "alert", "admin"}
	case "warning":
		targets = []string{"monitoring", "admin"}
	default:
		targets = []string{"monitoring", "dashboard"}
	}

	return ms.client.SendJSON("tagcast", "", targets, metric)
}

// SendSystemMetrics 发送系统指标
func (ms *MonitoringService) SendSystemMetrics(host string, metrics SystemMetrics) error {
	baseTime := time.Now()

	// CPU使用率
	cpuMetric := MetricData{
		Service: ms.name,
		Host:    host,
		Metric:  "cpu_usage",
		Value:   metrics.CPUUsage,
		Unit:    "percent",
		Level:   ms.getLevel(metrics.CPUUsage, 80, 95),
		Tags: map[string]string{
			"type": "system",
			"host": host,
		},
	}

	// 内存使用率
	memMetric := MetricData{
		Service: ms.name,
		Host:    host,
		Metric:  "memory_usage",
		Value:   metrics.MemoryUsage,
		Unit:    "percent",
		Level:   ms.getLevel(metrics.MemoryUsage, 85, 95),
		Tags: map[string]string{
			"type": "system",
			"host": host,
		},
	}

	// 磁盘使用率
	diskMetric := MetricData{
		Service: ms.name,
		Host:    host,
		Metric:  "disk_usage",
		Value:   metrics.DiskUsage,
		Unit:    "percent",
		Level:   ms.getLevel(metrics.DiskUsage, 90, 98),
		Tags: map[string]string{
			"type": "system",
			"host": host,
		},
	}

	// 发送指标
	metrics_list := []MetricData{cpuMetric, memMetric, diskMetric}
	for i, metric := range metrics_list {
		metric.Timestamp = baseTime.Add(time.Duration(i) * time.Millisecond * 100)
		if err := ms.SendMetric(metric); err != nil {
			return fmt.Errorf("failed to send %s metric: %w", metric.Metric, err)
		}
	}

	return nil
}

// getLevel 根据阈值确定指标级别
func (ms *MonitoringService) getLevel(value, warningThreshold, criticalThreshold float64) string {
	if value >= criticalThreshold {
		return "critical"
	} else if value >= warningThreshold {
		return "warning"
	}
	return "normal"
}

// generateRandomMetrics 生成随机的系统指标（用于演示）
func (ms *MonitoringService) generateRandomMetrics() SystemMetrics {
	return SystemMetrics{
		CPUUsage:    rand.Float64() * 100,
		MemoryUsage: rand.Float64() * 100,
		DiskUsage:   50 + rand.Float64()*40, // 50-90%
		NetworkIn:   rand.Float64() * 1000,  // MB/s
		NetworkOut:  rand.Float64() * 500,   // MB/s
	}
}

// RunDemo 运行监控演示
func (ms *MonitoringService) RunDemo(interval, duration time.Duration) {
	fmt.Println("Starting monitoring demo...")

	hosts := []string{"web-server-01", "web-server-02", "db-server-01", "cache-server-01"}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	timeout := time.After(duration)

	for {
		select {
		case <-timeout:
			fmt.Println("Demo completed!")
			return

		case <-ticker.C:
			fmt.Printf("[%s] Collecting and sending metrics...\n", time.Now().Format("15:04:05"))

			for _, host := range hosts {
				metrics := ms.generateRandomMetrics()

				if err := ms.SendSystemMetrics(host, metrics); err != nil {
					log.Printf("Failed to send metrics for %s: %v", host, err)
				} else {
					// 显示发送的指标摘要
					fmt.Printf("  %s: CPU=%.1f%%, MEM=%.1f%%, DISK=%.1f%%\n",
						host, metrics.CPUUsage, metrics.MemoryUsage, metrics.DiskUsage)
				}
			}

			// 发送一些应用指标
			appMetric := MetricData{
				Service: "web-app",
				Host:    "web-server-01",
				Metric:  "response_time",
				Value:   50 + rand.Float64()*200, // 50-250ms
				Unit:    "ms",
				Level:   "normal",
				Tags: map[string]string{
					"type":     "application",
					"endpoint": "/api/users",
				},
			}

			if err := ms.SendMetric(appMetric); err != nil {
				log.Printf("Failed to send app metric: %v", err)
			}

			fmt.Println()
		}
	}
}

// ShowStats 显示统计信息
func (ms *MonitoringService) ShowStats() {
	fmt.Println("Statistics:")
	fmt.Println("===========")

	// 客户端统计
	clientStats := ms.client.GetStats()
	fmt.Printf("Client Statistics:\n")
	fmt.Printf("  Metrics Sent: %d\n", clientStats.MessagesSent)
	fmt.Printf("  Total Requests: %d\n", clientStats.RequestsTotal)
	fmt.Printf("  Successful Requests: %d\n", clientStats.RequestsSuccess)
	fmt.Printf("  Failed Requests: %d\n", clientStats.RequestsFailed)

	if clientStats.RequestsTotal > 0 {
		successRate := float64(clientStats.RequestsSuccess) / float64(clientStats.RequestsTotal) * 100
		fmt.Printf("  Success Rate: %.2f%%\n", successRate)
	}

	if clientStats.LastError != nil {
		fmt.Printf("  Last Error: %v\n", clientStats.LastError)
	}

	// 服务器统计
	fmt.Printf("\nServer Statistics:\n")
	serverStats, err := ms.client.GetServerStats()
	if err != nil {
		fmt.Printf("  Failed to get server stats: %v\n", err)
	} else {
		fmt.Printf("  Node ID: %s\n", serverStats.NodeID)
		fmt.Printf("  Current Connections: %d\n", serverStats.CurrentConnections)
		fmt.Printf("  Total Connections: %d\n", serverStats.TotalConnections)
		fmt.Printf("  Messages Sent: %d\n", serverStats.MessagesSent)
		fmt.Printf("  Messages Received: %d\n", serverStats.MessagesReceived)
	}

	fmt.Printf("\nService Information:\n")
	fmt.Printf("  Service Name: %s\n", ms.name)
	fmt.Printf("  Device ID: %s\n", ms.deviceID)
}
