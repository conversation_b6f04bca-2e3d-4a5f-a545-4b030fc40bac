package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
	// 创建客户端配置
	config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
	config.Tags = []string{"example", "golang"}
	config.ClientInfo = "Go SDK Example v1.0.0"

	// 创建客户端
	client := qt_websocket_hub.NewClient(config)

	// 启动事件处理协程
	go handleEvents(client)

	// 连接到服务器
	fmt.Println("Connecting to WebSocket server...")
	if err := client.Connect(); err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}

	// 等待连接建立
	time.Sleep(2 * time.Second)

	if client.IsConnected() {
		fmt.Printf("Connected! Device ID: %s\n", client.GetDeviceID())

		// 发送一些测试消息
		sendTestMessages(client)
	} else {
		fmt.Println("Failed to establish connection")
		return
	}

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	fmt.Println("Client is running. Press Ctrl+C to exit...")
	<-sigChan

	// 断开连接
	fmt.Println("Disconnecting...")
	if err := client.Disconnect(); err != nil {
		log.Printf("Error during disconnect: %v", err)
	}

	fmt.Println("Disconnected successfully")
}

func handleEvents(client *qt_websocket_hub.Client) {
	for event := range client.Events() {
		switch event.Type {
		case qt_websocket_hub.EventConnected:
			if deviceID, ok := event.Data.(string); ok {
				fmt.Printf("[EVENT] Connected with device ID: %s\n", deviceID)
			} else {
				fmt.Printf("[EVENT] State changed to: %v\n", event.Data)
			}

		case qt_websocket_hub.EventDisconnected:
			fmt.Printf("[EVENT] Disconnected: %v\n", event.Data)

		case qt_websocket_hub.EventError:
			fmt.Printf("[EVENT] Error: %v\n", event.Error)

		case qt_websocket_hub.EventMessageReceived:
			if msgEvent, ok := event.Data.(qt_websocket_hub.MessageEvent); ok {
				fmt.Printf("[EVENT] Message received (type: %v): %s\n",
					msgEvent.Type, string(msgEvent.Payload))
			}
		}
	}
}

func sendTestMessages(client *qt_websocket_hub.Client) {
	// 发送广播消息
	fmt.Println("Sending broadcast message...")
	if err := client.SendBroadcast([]byte("Hello from Go SDK!")); err != nil {
		log.Printf("Failed to send broadcast: %v", err)
	}

	time.Sleep(1 * time.Second)

	// 发送标签组播消息
	fmt.Println("Sending tagcast message...")
	if err := client.SendTagcast([]string{"example", "test"}, []byte("Tagcast message from Go SDK")); err != nil {
		log.Printf("Failed to send tagcast: %v", err)
	}

	time.Sleep(1 * time.Second)

	// 如果有其他设备ID，可以发送单播消息
	// client.SendUnicast("target-device-id", []byte("Unicast message"))

	// 显示统计信息
	stats := client.GetStats()
	fmt.Printf("Stats: Sent=%d, Received=%d, Connected at=%v\n",
		stats.MessagesSent, stats.MessagesReceived, stats.ConnectedAt.Format(time.RFC3339))
}
