package main

import (
	"fmt"
	"log"

	qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
	// 创建客户端配置
	config := qt_websocket_hub.DefaultConfig("http://localhost:9090")
	config.Debug = true // 启用调试日志

	// 创建客户端
	client := qt_websocket_hub.NewClient(config)

	fmt.Println("Qt WebSocket Hub Go SDK - Simple Example")
	fmt.Println("========================================")

	// 1. 健康检查
	fmt.Println("\n1. Health Check:")
	health, err := client.HealthCheck()
	if err != nil {
		log.Fatalf("Health check failed: %v", err)
	}
	fmt.Printf("   Status: %s\n", health.Status)
	fmt.Printf("   Node ID: %s\n", health.NodeID)

	// 2. 创建设备ID
	fmt.Println("\n2. Creating Device ID:")
	deviceID, err := client.CreateDevice([]string{"example", "golang", "sdk"})
	if err != nil {
		log.Fatalf("Failed to create device: %v", err)
	}
	fmt.Printf("   Device ID: %s\n", deviceID)

	// 3. 发送测试消息
	fmt.Println("\n3. Sending Messages:")
	sendTestMessages(client)

	// 4. 获取服务器统计信息
	fmt.Println("\n4. Server Statistics:")
	stats, err := client.GetServerStats()
	if err != nil {
		log.Printf("Failed to get server stats: %v", err)
	} else {
		fmt.Printf("   Current Connections: %d\n", stats.CurrentConnections)
		fmt.Printf("   Total Connections: %d\n", stats.TotalConnections)
		fmt.Printf("   Messages Sent: %d\n", stats.MessagesSent)
		fmt.Printf("   Messages Received: %d\n", stats.MessagesReceived)
	}

	// 5. 客户端统计信息
	fmt.Println("\n5. Client Statistics:")
	clientStats := client.GetStats()
	fmt.Printf("   Messages Sent: %d\n", clientStats.MessagesSent)
	fmt.Printf("   Requests Total: %d\n", clientStats.RequestsTotal)
	fmt.Printf("   Requests Success: %d\n", clientStats.RequestsSuccess)
	fmt.Printf("   Requests Failed: %d\n", clientStats.RequestsFailed)

	fmt.Println("\nExample completed successfully!")
}

func sendTestMessages(client *qt_websocket_hub.Client) {
	// 发送广播消息
	fmt.Println("   Broadcasting message...")
	if err := client.SendBroadcast([]byte("Hello from Go SDK!")); err != nil {
		log.Printf("   Failed to send broadcast: %v", err)
	} else {
		fmt.Println("   ✓ Broadcast message sent")
	}

	// 发送标签组播消息
	fmt.Println("   Sending tagcast message...")
	if err := client.SendTagcast([]string{"example", "test"}, []byte("Tagcast message from Go SDK")); err != nil {
		log.Printf("   Failed to send tagcast: %v", err)
	} else {
		fmt.Println("   ✓ Tagcast message sent")
	}

	// 发送字符串消息（便捷方法）
	fmt.Println("   Sending string message...")
	if err := client.SendString("broadcast", "", nil, "Hello from SendString method!"); err != nil {
		log.Printf("   Failed to send string: %v", err)
	} else {
		fmt.Println("   ✓ String message sent")
	}

	// 发送JSON消息（便捷方法）
	fmt.Println("   Sending JSON message...")
	jsonData := map[string]interface{}{
		"type":    "notification",
		"title":   "Go SDK Test",
		"content": "This is a JSON message from Go SDK",
		"level":   "info",
	}
	if err := client.SendJSON("tagcast", "", []string{"example"}, jsonData); err != nil {
		log.Printf("   Failed to send JSON: %v", err)
	} else {
		fmt.Println("   ✓ JSON message sent")
	}

	// 测试分布式消息
	fmt.Println("   Sending distributed broadcast...")
	if err := client.SendBroadcastDistributed([]byte("Distributed message from Go SDK")); err != nil {
		log.Printf("   Failed to send distributed broadcast: %v", err)
	} else {
		fmt.Println("   ✓ Distributed broadcast sent")
	}
}
