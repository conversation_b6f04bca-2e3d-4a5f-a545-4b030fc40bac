package main

import (
	"flag"
	"fmt"
	"log"
	"time"

	qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

// NotificationService 通知服务
type NotificationService struct {
	client   *qt_websocket_hub.Client
	deviceID string
	name     string
}

// Notification 通知消息结构
type Notification struct {
	ID      string    `json:"id"`
	Type    string    `json:"type"`
	Title   string    `json:"title"`
	Content string    `json:"content"`
	Level   string    `json:"level"` // info, warning, error, success
	Time    time.Time `json:"time"`
	From    string    `json:"from"`
	Target  []string  `json:"target,omitempty"`
}

func main() {
	var (
		serverURL = flag.String("server", "http://localhost:9090", "WebSocket Hub server URL")
		debug     = flag.Bool("debug", false, "Enable debug logging")
		name      = flag.String("name", "notification-service", "Service name")
	)
	flag.Parse()

	fmt.Printf("Qt WebSocket Hub - Notification Service\n")
	fmt.Printf("======================================\n")
	fmt.Printf("Server: %s\n", *serverURL)
	fmt.Printf("Service: %s\n", *name)
	fmt.Printf("Debug: %v\n\n", *debug)

	// 创建通知服务
	service, err := NewNotificationService(*serverURL, *name, *debug)
	if err != nil {
		log.Fatalf("Failed to create notification service: %v", err)
	}

	fmt.Printf("Notification service started with device ID: %s\n\n", service.deviceID)

	// 发送各种类型的通知
	service.DemoNotifications()

	// 显示统计信息
	service.ShowStats()
}

// NewNotificationService 创建新的通知服务
func NewNotificationService(serverURL, name string, debug bool) (*NotificationService, error) {
	config := qt_websocket_hub.DefaultConfig(serverURL)
	config.Debug = debug
	config.UserAgent = fmt.Sprintf("NotificationService/%s v1.0.0", name)

	client := qt_websocket_hub.NewClient(config)

	// 健康检查
	health, err := client.HealthCheck()
	if err != nil {
		return nil, fmt.Errorf("health check failed: %w", err)
	}

	if health.Status != "healthy" {
		return nil, fmt.Errorf("server not healthy: %s", health.Status)
	}

	// 创建设备ID
	deviceID, err := client.CreateDevice([]string{"notification", "service", name})
	if err != nil {
		return nil, fmt.Errorf("failed to create device: %w", err)
	}

	return &NotificationService{
		client:   client,
		deviceID: deviceID,
		name:     name,
	}, nil
}

// SendNotification 发送通知
func (ns *NotificationService) SendNotification(notif Notification) error {
	notif.From = ns.name
	notif.Time = time.Now()

	if notif.ID == "" {
		notif.ID = fmt.Sprintf("notif_%d", time.Now().UnixNano())
	}

	// 根据目标发送
	if len(notif.Target) > 0 {
		return ns.client.SendJSON("tagcast", "", notif.Target, notif)
	} else {
		return ns.client.SendJSON("broadcast", "", nil, notif)
	}
}

// SendSystemNotification 发送系统通知
func (ns *NotificationService) SendSystemNotification(title, content, level string, target []string) error {
	notif := Notification{
		Type:    "system",
		Title:   title,
		Content: content,
		Level:   level,
		Target:  target,
	}
	return ns.SendNotification(notif)
}

// SendUserNotification 发送用户通知
func (ns *NotificationService) SendUserNotification(title, content string, target []string) error {
	notif := Notification{
		Type:    "user",
		Title:   title,
		Content: content,
		Level:   "info",
		Target:  target,
	}
	return ns.SendNotification(notif)
}

// SendAlert 发送警报
func (ns *NotificationService) SendAlert(title, content string, target []string) error {
	notif := Notification{
		Type:    "alert",
		Title:   title,
		Content: content,
		Level:   "error",
		Target:  target,
	}
	return ns.SendNotification(notif)
}

// DemoNotifications 演示各种通知
func (ns *NotificationService) DemoNotifications() {
	fmt.Println("Sending demo notifications...")

	// 1. 系统维护通知
	fmt.Println("1. System maintenance notification...")
	err := ns.SendSystemNotification(
		"System Maintenance",
		"The system will be under maintenance from 2:00 AM to 4:00 AM",
		"warning",
		[]string{"admin", "user"},
	)
	if err != nil {
		log.Printf("Failed to send maintenance notification: %v", err)
	} else {
		fmt.Println("   ✓ Sent")
	}

	time.Sleep(1 * time.Second)

	// 2. 用户欢迎通知
	fmt.Println("2. Welcome notification...")
	err = ns.SendUserNotification(
		"Welcome!",
		"Welcome to our platform! We're glad to have you here.",
		[]string{"new-user"},
	)
	if err != nil {
		log.Printf("Failed to send welcome notification: %v", err)
	} else {
		fmt.Println("   ✓ Sent")
	}

	time.Sleep(1 * time.Second)

	// 3. 安全警报
	fmt.Println("3. Security alert...")
	err = ns.SendAlert(
		"Security Alert",
		"Unusual login activity detected from IP *************",
		[]string{"admin", "security"},
	)
	if err != nil {
		log.Printf("Failed to send security alert: %v", err)
	} else {
		fmt.Println("   ✓ Sent")
	}

	time.Sleep(1 * time.Second)

	// 4. 广播通知
	fmt.Println("4. Broadcast notification...")
	err = ns.SendSystemNotification(
		"New Feature Available",
		"We've just released a new feature! Check it out in your dashboard.",
		"success",
		nil, // 广播给所有用户
	)
	if err != nil {
		log.Printf("Failed to send broadcast notification: %v", err)
	} else {
		fmt.Println("   ✓ Sent")
	}

	time.Sleep(1 * time.Second)

	// 5. 自定义通知
	fmt.Println("5. Custom notification...")
	customNotif := Notification{
		Type:    "custom",
		Title:   "Custom Event",
		Content: "This is a custom notification with additional data",
		Level:   "info",
		Target:  []string{"developer", "admin"},
	}
	err = ns.SendNotification(customNotif)
	if err != nil {
		log.Printf("Failed to send custom notification: %v", err)
	} else {
		fmt.Println("   ✓ Sent")
	}

	fmt.Println()
}

// ShowStats 显示统计信息
func (ns *NotificationService) ShowStats() {
	fmt.Println("Statistics:")
	fmt.Println("===========")

	// 客户端统计
	clientStats := ns.client.GetStats()
	fmt.Printf("Client Statistics:\n")
	fmt.Printf("  Messages Sent: %d\n", clientStats.MessagesSent)
	fmt.Printf("  Total Requests: %d\n", clientStats.RequestsTotal)
	fmt.Printf("  Successful Requests: %d\n", clientStats.RequestsSuccess)
	fmt.Printf("  Failed Requests: %d\n", clientStats.RequestsFailed)

	if clientStats.RequestsTotal > 0 {
		successRate := float64(clientStats.RequestsSuccess) / float64(clientStats.RequestsTotal) * 100
		fmt.Printf("  Success Rate: %.2f%%\n", successRate)
	}

	if clientStats.LastError != nil {
		fmt.Printf("  Last Error: %v\n", clientStats.LastError)
	}

	// 服务器统计
	fmt.Printf("\nServer Statistics:\n")
	serverStats, err := ns.client.GetServerStats()
	if err != nil {
		fmt.Printf("  Failed to get server stats: %v\n", err)
	} else {
		fmt.Printf("  Node ID: %s\n", serverStats.NodeID)
		fmt.Printf("  Current Connections: %d\n", serverStats.CurrentConnections)
		fmt.Printf("  Total Connections: %d\n", serverStats.TotalConnections)
		fmt.Printf("  Messages Sent: %d\n", serverStats.MessagesSent)
		fmt.Printf("  Messages Received: %d\n", serverStats.MessagesReceived)
		fmt.Printf("  Kafka Enabled: %v\n", serverStats.KafkaEnabled)
	}

	fmt.Printf("\nService Information:\n")
	fmt.Printf("  Service Name: %s\n", ns.name)
	fmt.Printf("  Device ID: %s\n", ns.deviceID)
}
