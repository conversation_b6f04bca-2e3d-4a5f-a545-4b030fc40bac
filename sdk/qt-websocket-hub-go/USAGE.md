# Qt WebSocket Hub Go SDK 使用指南

## 快速开始

### 1. 安装SDK

```bash
go get github.com/your-username/qt-websocket-hub-go
```

### 2. 基本使用

```go
package main

import (
    "fmt"
    "log"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

func main() {
    // 创建配置
    config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
    config.Tags = []string{"my-app", "production"}
    
    // 创建客户端
    client := qt_websocket_hub.NewClient(config)
    
    // 连接服务器
    if err := client.Connect(); err != nil {
        log.Fatalf("连接失败: %v", err)
    }
    defer client.Disconnect()
    
    fmt.Printf("已连接，设备ID: %s\n", client.GetDeviceID())
    
    // 发送消息
    client.SendBroadcast([]byte("Hello, World!"))
}
```

## 消息发送

### 广播消息

```go
// 发送给所有连接的设备
err := client.SendBroadcast([]byte("广播消息"))
```

### 单播消息

```go
// 发送给特定设备
err := client.SendUnicast("target-device-id", []byte("私人消息"))
```

### 标签组播

```go
// 发送给具有特定标签的设备
err := client.SendTagcast([]string{"admin", "moderator"}, []byte("管理员消息"))
```

## 事件处理

```go
// 处理事件
go func() {
    for event := range client.Events() {
        switch event.Type {
        case qt_websocket_hub.EventConnected:
            fmt.Printf("已连接: %v\n", event.Data)
            
        case qt_websocket_hub.EventDisconnected:
            fmt.Printf("连接断开: %v\n", event.Data)
            
        case qt_websocket_hub.EventError:
            fmt.Printf("错误: %v\n", event.Error)
            
        case qt_websocket_hub.EventMessageReceived:
            if msgEvent, ok := event.Data.(qt_websocket_hub.MessageEvent); ok {
                fmt.Printf("收到消息: %s\n", string(msgEvent.Payload))
            }
        }
    }
}()
```

## 配置选项

### 默认配置

```go
config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
```

### 自定义配置

```go
config := &qt_websocket_hub.ClientConfig{
    ServerURL:            "ws://example.com:8080/api/v1/ws",
    Tags:                 []string{"my-app", "v1.0"},
    ClientInfo:           "我的应用 v1.0.0",
    ConnectTimeout:       10 * time.Second,
    WriteTimeout:         5 * time.Second,
    HeartbeatInterval:    30 * time.Second,
    AutoReconnect:        true,
    ReconnectInterval:    5 * time.Second,
    MaxReconnectAttempts: 10,
}
```

## 实际应用示例

### 聊天应用

```go
package main

import (
    "encoding/json"
    "fmt"
    "time"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

type ChatMessage struct {
    From    string    `json:"from"`
    Content string    `json:"content"`
    Time    time.Time `json:"time"`
    Room    string    `json:"room"`
}

func main() {
    config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
    config.Tags = []string{"chat", "general"}
    config.ClientInfo = "聊天客户端 v1.0.0"
    
    client := qt_websocket_hub.NewClient(config)
    
    // 处理收到的消息
    go func() {
        for event := range client.Events() {
            if event.Type == qt_websocket_hub.EventMessageReceived {
                if msgEvent, ok := event.Data.(qt_websocket_hub.MessageEvent); ok {
                    var chatMsg ChatMessage
                    if err := json.Unmarshal(msgEvent.Payload, &chatMsg); err == nil {
                        fmt.Printf("[%s] %s: %s\n", 
                            chatMsg.Time.Format("15:04:05"), 
                            chatMsg.From, 
                            chatMsg.Content)
                    }
                }
            }
        }
    }()
    
    if err := client.Connect(); err != nil {
        panic(err)
    }
    defer client.Disconnect()
    
    // 发送聊天消息
    msg := ChatMessage{
        From:    "GoUser",
        Content: "Hello from Go!",
        Time:    time.Now(),
        Room:    "general",
    }
    
    data, _ := json.Marshal(msg)
    client.SendTagcast([]string{"general"}, data)
    
    // 保持运行
    select {}
}
```

### 通知系统

```go
package main

import (
    "encoding/json"
    "time"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

type Notification struct {
    ID       string    `json:"id"`
    Title    string    `json:"title"`
    Content  string    `json:"content"`
    Level    string    `json:"level"` // info, warning, error
    Time     time.Time `json:"time"`
    Target   []string  `json:"target"` // 目标用户标签
}

func main() {
    config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
    config.Tags = []string{"notification-service"}
    config.ClientInfo = "通知服务 v1.0.0"
    
    client := qt_websocket_hub.NewClient(config)
    
    if err := client.Connect(); err != nil {
        panic(err)
    }
    defer client.Disconnect()
    
    // 发送系统通知
    notification := Notification{
        ID:      "notif-001",
        Title:   "系统维护",
        Content: "系统将在今晚22:00进行维护",
        Level:   "warning",
        Time:    time.Now(),
        Target:  []string{"user", "admin"},
    }
    
    data, _ := json.Marshal(notification)
    client.SendTagcast([]string{"user", "admin"}, data)
}
```

### 监控系统

```go
package main

import (
    "encoding/json"
    "time"
    
    qt_websocket_hub "github.com/your-username/qt-websocket-hub-go"
)

type MetricData struct {
    Service   string    `json:"service"`
    Metric    string    `json:"metric"`
    Value     float64   `json:"value"`
    Unit      string    `json:"unit"`
    Timestamp time.Time `json:"timestamp"`
    Tags      []string  `json:"tags"`
}

func main() {
    config := qt_websocket_hub.DefaultConfig("ws://localhost:9090/api/v1/ws")
    config.Tags = []string{"monitoring", "metrics"}
    config.ClientInfo = "监控客户端 v1.0.0"
    
    client := qt_websocket_hub.NewClient(config)
    
    if err := client.Connect(); err != nil {
        panic(err)
    }
    defer client.Disconnect()
    
    // 定期发送监控数据
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        metric := MetricData{
            Service:   "web-server",
            Metric:    "cpu_usage",
            Value:     75.5,
            Unit:      "percent",
            Timestamp: time.Now(),
            Tags:      []string{"production", "server-01"},
        }
        
        data, _ := json.Marshal(metric)
        client.SendTagcast([]string{"monitoring", "dashboard"}, data)
    }
}
```

## 错误处理

### 连接错误

```go
if err := client.Connect(); err != nil {
    switch {
    case strings.Contains(err.Error(), "connection refused"):
        log.Println("服务器未启动或地址错误")
    case strings.Contains(err.Error(), "timeout"):
        log.Println("连接超时")
    default:
        log.Printf("连接失败: %v", err)
    }
}
```

### 运行时错误

```go
go func() {
    for event := range client.Events() {
        if event.Type == qt_websocket_hub.EventError {
            log.Printf("运行时错误: %v", event.Error)
            
            // 根据错误类型进行处理
            if strings.Contains(event.Error.Error(), "connection lost") {
                // 连接丢失，等待自动重连
                log.Println("连接丢失，等待重连...")
            }
        }
    }
}()
```

### 发送错误

```go
if err := client.SendBroadcast(data); err != nil {
    if !client.IsConnected() {
        log.Println("未连接到服务器")
    } else {
        log.Printf("发送失败: %v", err)
    }
}
```

## 最佳实践

### 1. 优雅关闭

```go
// 监听系统信号
sigChan := make(chan os.Signal, 1)
signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

go func() {
    <-sigChan
    log.Println("正在关闭...")
    client.Disconnect()
    os.Exit(0)
}()
```

### 2. 连接状态监控

```go
go func() {
    for event := range client.Events() {
        switch event.Type {
        case qt_websocket_hub.EventConnected:
            log.Println("✓ 连接成功")
        case qt_websocket_hub.EventDisconnected:
            log.Println("✗ 连接断开")
        case qt_websocket_hub.EventError:
            log.Printf("✗ 错误: %v", event.Error)
        }
    }
}()
```

### 3. 消息队列

```go
type MessageQueue struct {
    client   *qt_websocket_hub.Client
    queue    chan []byte
    maxSize  int
}

func (mq *MessageQueue) Send(data []byte) {
    select {
    case mq.queue <- data:
        // 成功加入队列
    default:
        // 队列满，丢弃消息或处理
        log.Println("消息队列已满")
    }
}

func (mq *MessageQueue) Start() {
    for data := range mq.queue {
        if mq.client.IsConnected() {
            mq.client.SendBroadcast(data)
        }
    }
}
```

## 性能优化

### 1. 批量发送

```go
// 收集消息后批量发送
messages := [][]byte{}
for i := 0; i < 100; i++ {
    messages = append(messages, []byte(fmt.Sprintf("Message %d", i)))
}

// 批量发送
for _, msg := range messages {
    client.SendBroadcast(msg)
}
```

### 2. 连接池

```go
type ClientPool struct {
    clients []*qt_websocket_hub.Client
    current int
    mu      sync.Mutex
}

func (cp *ClientPool) GetClient() *qt_websocket_hub.Client {
    cp.mu.Lock()
    defer cp.mu.Unlock()
    
    client := cp.clients[cp.current]
    cp.current = (cp.current + 1) % len(cp.clients)
    return client
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器地址和端口
   - 确认服务器正在运行
   - 检查网络连接

2. **消息发送失败**
   - 确认客户端已连接
   - 检查消息大小限制
   - 验证目标设备ID或标签

3. **自动重连不工作**
   - 检查`AutoReconnect`配置
   - 验证`MaxReconnectAttempts`设置
   - 查看错误日志

### 调试技巧

```go
// 启用详细日志
config.ClientInfo = "Debug Client v1.0.0"

// 监控统计信息
go func() {
    ticker := time.NewTicker(10 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        stats := client.GetStats()
        log.Printf("统计: 发送=%d, 接收=%d, 重连=%d", 
            stats.MessagesSent, stats.MessagesReceived, stats.ReconnectCount)
    }
}()
```
