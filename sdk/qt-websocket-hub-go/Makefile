# Qt WebSocket Hub Go SDK Makefile

.PHONY: all build test clean examples lint fmt vet deps help

# Go parameters
GOCMD=/home/<USER>/sdk/go1.24.3/bin/go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=$(GOCMD) fmt
GOVET=$(GOCMD) vet

# Build info
VERSION ?= v1.0.0
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Directories
EXAMPLES_DIR = examples
BUILD_DIR = build

all: deps test build examples ## Run deps, test, build and examples

deps: ## Download dependencies
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

test: ## Run tests
	@echo "Running tests..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...

test-coverage: test ## Run tests with coverage report
	@echo "Generating coverage report..."
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

build: ## Build the SDK (validation build)
	@echo "Building SDK..."
	$(GOBUILD) -v ./...

examples: ## Build all examples
	@echo "Building examples..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) -o $(BUILD_DIR)/simple-example $(EXAMPLES_DIR)/simple/main.go
	$(GOBUILD) -o $(BUILD_DIR)/advanced-example $(EXAMPLES_DIR)/advanced/main.go
	$(GOBUILD) -o $(BUILD_DIR)/notification-service $(EXAMPLES_DIR)/notification-service/main.go
	$(GOBUILD) -o $(BUILD_DIR)/monitoring-service $(EXAMPLES_DIR)/monitoring-service/main.go
	@echo "Examples built in $(BUILD_DIR)/"

clean: ## Clean build artifacts
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

fmt: ## Format code
	@echo "Formatting code..."
	$(GOFMT) ./...

vet: ## Run go vet
	@echo "Running go vet..."
	$(GOVET) ./...

lint: fmt vet ## Run linting tools

benchmark: ## Run benchmarks
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

run-simple: examples ## Run simple example
	@echo "Running simple example..."
	./$(BUILD_DIR)/simple-example

run-advanced: examples ## Run advanced example (requires username and room)
	@echo "Running advanced example..."
	@echo "Usage: make run-advanced USERNAME=alice ROOM=general"
	@if [ -z "$(USERNAME)" ] || [ -z "$(ROOM)" ]; then \
		echo "Please provide USERNAME and ROOM: make run-advanced USERNAME=alice ROOM=general"; \
		exit 1; \
	fi
	./$(BUILD_DIR)/advanced-example $(USERNAME) $(ROOM)

run-notification: examples ## Run notification service example
	@echo "Running notification service example..."
	./$(BUILD_DIR)/notification-service

run-monitoring: examples ## Run monitoring service example
	@echo "Running monitoring service example..."
	./$(BUILD_DIR)/monitoring-service

install-tools: ## Install development tools
	@echo "Installing development tools..."
	$(GOGET) -u golang.org/x/tools/cmd/goimports
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint

check: lint test ## Run all checks (lint and test)

release-check: clean deps check examples ## Full release check
	@echo "Release check completed successfully!"

# Docker targets
docker-build: ## Build Docker image for testing
	@echo "Building Docker image..."
	docker build -t qt-websocket-hub-go-sdk .

docker-test: docker-build ## Run tests in Docker
	@echo "Running tests in Docker..."
	docker run --rm qt-websocket-hub-go-sdk make test

# Documentation
docs: ## Generate documentation
	@echo "Generating documentation..."
	$(GOCMD) doc -all . > API.md
	@echo "Documentation generated: API.md"

# Git helpers
tag: ## Create a git tag (usage: make tag VERSION=v1.0.1)
	@if [ -z "$(VERSION)" ]; then \
		echo "Please provide VERSION: make tag VERSION=v1.0.1"; \
		exit 1; \
	fi
	git tag -a $(VERSION) -m "Release $(VERSION)"
	git push origin $(VERSION)

# Development helpers
dev-setup: deps install-tools ## Setup development environment
	@echo "Development environment setup complete!"

watch-test: ## Watch for changes and run tests
	@echo "Watching for changes..."
	@while true; do \
		$(GOTEST) -v ./...; \
		inotifywait -qre modify .; \
	done

# Help
help: ## Show this help message
	@echo "Qt WebSocket Hub Go SDK"
	@echo "======================="
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*?##/ { printf "  %-20s %s\n", $$1, $$2 }' $(MAKEFILE_LIST)
	@echo ""
	@echo "Examples:"
	@echo "  make test                    # Run tests"
	@echo "  make examples               # Build examples"
	@echo "  make run-simple             # Run simple example"
	@echo "  make run-advanced USERNAME=alice ROOM=general"
	@echo "  make release-check          # Full release validation"
	@echo ""

# Default target
.DEFAULT_GOAL := help
