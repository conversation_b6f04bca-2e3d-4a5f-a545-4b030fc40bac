package qt_websocket_hub

import "time"

// MessageType 消息类型枚举
type MessageType uint8

const (
	// 连接相关消息
	MsgTypeHandshake     MessageType = 0x01 // 握手消息
	MsgTypeHandshakeResp MessageType = 0x02 // 握手响应
	MsgTypeHeartbeat     MessageType = 0x03 // 心跳消息
	MsgTypeDisconnect    MessageType = 0x04 // 断开连接

	// 数据传输消息
	MsgTypeUnicast   MessageType = 0x10 // 单播消息
	MsgTypeBroadcast MessageType = 0x11 // 广播消息
	MsgTypeTagcast   MessageType = 0x12 // 基于标签的组播消息

	// 系统消息
	MsgTypeError MessageType = 0xF0 // 错误消息
	MsgTypeAck   MessageType = 0xF1 // 确认消息
)

// 标志位定义
const (
	FlagCompressed = 1 << 0 // 压缩标志
	FlagEncrypted  = 1 << 1 // 加密标志
	FlagReliable   = 1 << 2 // 可靠传输标志
)

// 协议常量
const (
	MessageMagic   = 0x5757 // 'WW'
	MessageVersion = 0x01
	HeaderSize     = 16
)

// MessageHeader 消息头结构
type MessageHeader struct {
	Magic      uint16      // 魔数 0x5757 ('WW')
	Version    uint8       // 协议版本
	Type       MessageType // 消息类型
	Flags      uint8       // 标志位
	Reserved   uint8       // 保留字段
	PayloadLen uint32      // 载荷长度
	Timestamp  uint32      // 时间戳
	Checksum   uint16      // 校验和
}

// Message 二进制消息结构
type Message struct {
	Header  MessageHeader
	Payload []byte
}

// HandshakePayload 握手消息载荷
type HandshakePayload struct {
	DeviceID   string   `json:"device_id,omitempty"`
	Tags       []string `json:"tags"`
	ClientInfo string   `json:"client_info"`
}

// HandshakeResponse 握手响应载荷
type HandshakeResponse struct {
	DeviceID  string `json:"device_id"`
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
}

// UnicastPayload 单播消息载荷
type UnicastPayload struct {
	TargetDeviceID string `json:"target_device_id"`
	Data           []byte `json:"data"`
}

// BroadcastPayload 广播消息载荷
type BroadcastPayload struct {
	Data []byte `json:"data"`
}

// TagcastPayload 标签组播消息载荷
type TagcastPayload struct {
	Tags []string `json:"tags"`
	Data []byte   `json:"data"`
}

// ErrorPayload 错误消息载荷
type ErrorPayload struct {
	Code    uint32 `json:"code"`
	Message string `json:"message"`
}

// ClientConfig 客户端配置
type ClientConfig struct {
	// 服务器地址
	ServerURL string

	// 连接标签
	Tags []string

	// 现有设备ID（可选）
	DeviceID string

	// 客户端信息
	ClientInfo string

	// 连接超时
	ConnectTimeout time.Duration

	// 写入超时
	WriteTimeout time.Duration

	// 心跳间隔
	HeartbeatInterval time.Duration

	// 是否启用自动重连
	AutoReconnect bool

	// 重连间隔
	ReconnectInterval time.Duration

	// 最大重连次数
	MaxReconnectAttempts int
}

// DefaultConfig 返回默认配置
func DefaultConfig(serverURL string) *ClientConfig {
	return &ClientConfig{
		ServerURL:            serverURL,
		Tags:                 []string{},
		ClientInfo:           "Qt WebSocket Hub Go SDK v1.0.0",
		ConnectTimeout:       10 * time.Second,
		WriteTimeout:         10 * time.Second,
		HeartbeatInterval:    30 * time.Second,
		AutoReconnect:        true,
		ReconnectInterval:    5 * time.Second,
		MaxReconnectAttempts: 10,
	}
}

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateError
)

// String 返回连接状态的字符串表示
func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "disconnected"
	case StateConnecting:
		return "connecting"
	case StateConnected:
		return "connected"
	case StateReconnecting:
		return "reconnecting"
	case StateError:
		return "error"
	default:
		return "unknown"
	}
}

// EventType 事件类型
type EventType int

const (
	EventConnected EventType = iota
	EventDisconnected
	EventError
	EventMessageReceived
)

// Event 事件结构
type Event struct {
	Type      EventType
	Data      interface{}
	Error     error
	Timestamp time.Time
}

// MessageEvent 消息事件数据
type MessageEvent struct {
	Type    MessageType
	Payload []byte
	Source  string // 源设备ID（如果有）
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	ConnectedAt      time.Time
	LastHeartbeat    time.Time
	MessagesSent     int64
	MessagesReceived int64
	ReconnectCount   int
	LastError        error
}
