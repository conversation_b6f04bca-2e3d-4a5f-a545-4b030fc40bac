package qt_websocket_hub

import "time"

// MessageType 消息类型枚举
type MessageType uint8

const (
	// 连接相关消息
	MsgTypeHandshake     MessageType = 0x01 // 握手消息
	MsgTypeHandshakeResp MessageType = 0x02 // 握手响应
	MsgTypeHeartbeat     MessageType = 0x03 // 心跳消息
	MsgTypeDisconnect    MessageType = 0x04 // 断开连接

	// 数据传输消息
	MsgTypeUnicast   MessageType = 0x10 // 单播消息
	MsgTypeBroadcast MessageType = 0x11 // 广播消息
	MsgTypeTagcast   MessageType = 0x12 // 基于标签的组播消息

	// 系统消息
	MsgTypeError MessageType = 0xF0 // 错误消息
	MsgTypeAck   MessageType = 0xF1 // 确认消息
)

// 标志位定义
const (
	FlagCompressed = 1 << 0 // 压缩标志
	FlagEncrypted  = 1 << 1 // 加密标志
	FlagReliable   = 1 << 2 // 可靠传输标志
)

// 协议常量
const (
	MessageMagic   = 0x5757 // 'WW'
	MessageVersion = 0x01
	HeaderSize     = 16
)

// MessageHeader 消息头结构
type MessageHeader struct {
	Magic      uint16      // 魔数 0x5757 ('WW')
	Version    uint8       // 协议版本
	Type       MessageType // 消息类型
	Flags      uint8       // 标志位
	Reserved   uint8       // 保留字段
	PayloadLen uint32      // 载荷长度
	Timestamp  uint32      // 时间戳
	Checksum   uint16      // 校验和
}

// Message 二进制消息结构
type Message struct {
	Header  MessageHeader
	Payload []byte
}

// HandshakePayload 握手消息载荷
type HandshakePayload struct {
	DeviceID   string   `json:"device_id,omitempty"`
	Tags       []string `json:"tags"`
	ClientInfo string   `json:"client_info"`
}

// HandshakeResponse 握手响应载荷
type HandshakeResponse struct {
	DeviceID  string `json:"device_id"`
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
}

// UnicastPayload 单播消息载荷
type UnicastPayload struct {
	TargetDeviceID string `json:"target_device_id"`
	Data           []byte `json:"data"`
}

// BroadcastPayload 广播消息载荷
type BroadcastPayload struct {
	Data []byte `json:"data"`
}

// TagcastPayload 标签组播消息载荷
type TagcastPayload struct {
	Tags []string `json:"tags"`
	Data []byte   `json:"data"`
}

// ErrorPayload 错误消息载荷
type ErrorPayload struct {
	Code    uint32 `json:"code"`
	Message string `json:"message"`
}

// ClientConfig HTTP客户端配置
type ClientConfig struct {
	// 服务器地址 (HTTP)
	ServerURL string

	// HTTP请求超时
	Timeout time.Duration

	// 用户代理
	UserAgent string

	// 是否启用调试日志
	Debug bool
}

// DefaultConfig 返回默认配置
func DefaultConfig(serverURL string) *ClientConfig {
	return &ClientConfig{
		ServerURL: serverURL,
		Timeout:   30 * time.Second,
		UserAgent: "Qt WebSocket Hub Go SDK v1.0.0",
		Debug:     false,
	}
}

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateError
)

// String 返回连接状态的字符串表示
func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "disconnected"
	case StateConnecting:
		return "connecting"
	case StateConnected:
		return "connected"
	case StateReconnecting:
		return "reconnecting"
	case StateError:
		return "error"
	default:
		return "unknown"
	}
}

// EventType 事件类型
type EventType int

const (
	EventConnected EventType = iota
	EventDisconnected
	EventError
	EventMessageReceived
)

// Event 事件结构
type Event struct {
	Type      EventType
	Data      interface{}
	Error     error
	Timestamp time.Time
}

// MessageEvent 消息事件数据
type MessageEvent struct {
	Type    MessageType
	Payload []byte
	Source  string // 源设备ID（如果有）
}

// HTTPResponse HTTP响应结构
type HTTPResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// DeviceCreateRequest 创建设备请求
type DeviceCreateRequest struct {
	Tags       []string `json:"tags,omitempty"`
	RemoteAddr string   `json:"remote_addr,omitempty"`
	UserAgent  string   `json:"user_agent,omitempty"`
}

// DeviceCreateResponse 创建设备响应
type DeviceCreateResponse struct {
	DeviceID string `json:"device_id"`
}

// MessageSendRequest 发送消息请求
type MessageSendRequest struct {
	Type           string   `json:"type"`                      // "unicast", "broadcast", "tagcast"
	TargetDeviceID string   `json:"target_device_id,omitempty"` // 单播目标设备ID
	Tags           []string `json:"tags,omitempty"`            // 标签组播目标标签
	Data           string   `json:"data"`                      // Base64编码的消息数据
	Distributed   bool     `json:"distributed,omitempty"`     // 是否分布式发送
}

// StatsResponse 统计信息响应
type StatsResponse struct {
	NodeID              string    `json:"node_id"`
	StartTime           time.Time `json:"start_time"`
	CurrentConnections  int       `json:"current_connections"`
	TotalConnections    int64     `json:"total_connections"`
	MessagesSent        int64     `json:"messages_sent"`
	MessagesReceived    int64     `json:"messages_received"`
	KafkaEnabled        bool      `json:"kafka_enabled"`
	ConnectedNodes      int       `json:"connected_nodes,omitempty"`
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version,omitempty"`
	NodeID    string    `json:"node_id,omitempty"`
}

// ClientStats 客户端统计信息
type ClientStats struct {
	MessagesSent     int64
	RequestsTotal    int64
	RequestsSuccess  int64
	RequestsFailed   int64
	LastRequestTime  time.Time
	LastError        error
}
