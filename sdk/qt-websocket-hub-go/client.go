package qt_websocket_hub

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Client WebSocket客户端
type Client struct {
	config *ClientConfig
	conn   *websocket.Conn

	// 状态管理
	state    ConnectionState
	deviceID string

	// 事件处理
	eventChan chan Event

	// 统计信息
	stats ConnectionStats

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 重连控制
	reconnectCount int
}

// NewClient 创建新的WebSocket客户端
func NewClient(config *ClientConfig) *Client {
	if config == nil {
		config = DefaultConfig("")
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &Client{
		config:    config,
		state:     StateDisconnected,
		eventChan: make(chan Event, 100),
		ctx:       ctx,
		cancel:    cancel,
	}
}

// Connect 连接到WebSocket服务器
func (c *Client) Connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.state == StateConnected || c.state == StateConnecting {
		return fmt.Errorf("already connected or connecting")
	}

	c.setState(StateConnecting)

	// 解析URL
	u, err := url.Parse(c.config.ServerURL)
	if err != nil {
		c.setState(StateError)
		return fmt.Errorf("invalid server URL: %w", err)
	}

	// 建立WebSocket连接
	dialer := websocket.Dialer{
		HandshakeTimeout: c.config.ConnectTimeout,
	}

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		c.setState(StateError)
		return fmt.Errorf("failed to connect: %w", err)
	}

	c.conn = conn

	// 发送握手消息
	if err := c.sendHandshake(); err != nil {
		conn.Close()
		c.setState(StateError)
		return fmt.Errorf("handshake failed: %w", err)
	}

	// 启动消息处理协程
	c.wg.Add(2)
	go c.readLoop()
	go c.heartbeatLoop()

	return nil
}

// Disconnect 断开连接
func (c *Client) Disconnect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.state == StateDisconnected {
		return nil
	}

	// 发送断开连接消息
	if c.conn != nil && c.state == StateConnected {
		disconnectMsg := NewMessage(MsgTypeDisconnect, nil)
		if data, err := disconnectMsg.Marshal(); err == nil {
			c.conn.WriteMessage(websocket.BinaryMessage, data)
		}
	}

	c.cancel()

	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.setState(StateDisconnected)
	c.wg.Wait()

	return nil
}

// SendUnicast 发送单播消息
func (c *Client) SendUnicast(targetDeviceID string, data []byte) error {
	if !c.IsConnected() {
		return fmt.Errorf("not connected")
	}

	payload := UnicastPayload{
		TargetDeviceID: targetDeviceID,
		Data:           data,
	}

	return c.sendMessage(MsgTypeUnicast, payload)
}

// SendBroadcast 发送广播消息
func (c *Client) SendBroadcast(data []byte) error {
	if !c.IsConnected() {
		return fmt.Errorf("not connected")
	}

	payload := BroadcastPayload{
		Data: data,
	}

	return c.sendMessage(MsgTypeBroadcast, payload)
}

// SendTagcast 发送标签组播消息
func (c *Client) SendTagcast(tags []string, data []byte) error {
	if !c.IsConnected() {
		return fmt.Errorf("not connected")
	}

	payload := TagcastPayload{
		Tags: tags,
		Data: data,
	}

	return c.sendMessage(MsgTypeTagcast, payload)
}

// IsConnected 检查是否已连接
func (c *Client) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.state == StateConnected
}

// GetState 获取连接状态
func (c *Client) GetState() ConnectionState {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.state
}

// GetDeviceID 获取设备ID
func (c *Client) GetDeviceID() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.deviceID
}

// GetStats 获取连接统计信息
func (c *Client) GetStats() ConnectionStats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.stats
}

// Events 获取事件通道
func (c *Client) Events() <-chan Event {
	return c.eventChan
}

// 私有方法

func (c *Client) setState(state ConnectionState) {
	c.state = state
	c.emitEvent(Event{
		Type:      EventConnected,
		Data:      state,
		Timestamp: time.Now(),
	})
}

func (c *Client) sendHandshake() error {
	handshake := HandshakePayload{
		DeviceID:   c.config.DeviceID,
		Tags:       c.config.Tags,
		ClientInfo: c.config.ClientInfo,
	}

	payloadData, err := json.Marshal(handshake)
	if err != nil {
		return fmt.Errorf("failed to marshal handshake: %w", err)
	}

	msg := NewMessage(MsgTypeHandshake, payloadData)
	data, err := msg.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	if err := c.conn.WriteMessage(websocket.BinaryMessage, data); err != nil {
		return fmt.Errorf("failed to send handshake: %w", err)
	}

	// 等待握手响应
	c.conn.SetReadDeadline(time.Now().Add(c.config.ConnectTimeout))
	_, respData, err := c.conn.ReadMessage()
	if err != nil {
		return fmt.Errorf("failed to read handshake response: %w", err)
	}

	respMsg, err := UnmarshalMessage(respData)
	if err != nil {
		return fmt.Errorf("failed to unmarshal handshake response: %w", err)
	}

	if respMsg.GetType() != MsgTypeHandshakeResp {
		return fmt.Errorf("unexpected handshake response type: %v", respMsg.GetType())
	}

	var handshakeResp HandshakeResponse
	if err := json.Unmarshal(respMsg.GetPayload(), &handshakeResp); err != nil {
		return fmt.Errorf("failed to unmarshal handshake response payload: %w", err)
	}

	if handshakeResp.Status != "connected" {
		return fmt.Errorf("handshake failed: %s", handshakeResp.Status)
	}

	c.deviceID = handshakeResp.DeviceID
	c.setState(StateConnected)
	c.stats.ConnectedAt = time.Now()
	c.reconnectCount = 0

	c.emitEvent(Event{
		Type:      EventConnected,
		Data:      c.deviceID,
		Timestamp: time.Now(),
	})

	return nil
}

func (c *Client) sendMessage(msgType MessageType, payload interface{}) error {
	payloadData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	msg := NewMessage(msgType, payloadData)
	data, err := msg.Marshal()
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	if err := c.conn.WriteMessage(websocket.BinaryMessage, data); err != nil {
		return fmt.Errorf("failed to send message: %w", err)
	}

	c.mu.Lock()
	c.stats.MessagesSent++
	c.mu.Unlock()

	return nil
}

func (c *Client) readLoop() {
	defer c.wg.Done()

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			_, data, err := c.conn.ReadMessage()
			if err != nil {
				if !websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
					c.emitEvent(Event{
						Type:      EventError,
						Error:     err,
						Timestamp: time.Now(),
					})
				}
				return
			}

			c.handleMessage(data)
		}
	}
}

func (c *Client) handleMessage(data []byte) {
	msg, err := UnmarshalMessage(data)
	if err != nil {
		c.emitEvent(Event{
			Type:      EventError,
			Error:     fmt.Errorf("failed to unmarshal message: %w", err),
			Timestamp: time.Now(),
		})
		return
	}

	c.mu.Lock()
	c.stats.MessagesReceived++
	c.mu.Unlock()

	switch msg.GetType() {
	case MsgTypeHeartbeat:
		c.handleHeartbeat()
	case MsgTypeUnicast, MsgTypeBroadcast, MsgTypeTagcast:
		c.emitEvent(Event{
			Type: EventMessageReceived,
			Data: MessageEvent{
				Type:    msg.GetType(),
				Payload: msg.GetPayload(),
			},
			Timestamp: time.Now(),
		})
	case MsgTypeError:
		var errorPayload ErrorPayload
		if err := json.Unmarshal(msg.GetPayload(), &errorPayload); err == nil {
			c.emitEvent(Event{
				Type:      EventError,
				Error:     fmt.Errorf("server error: %s (code: %d)", errorPayload.Message, errorPayload.Code),
				Timestamp: time.Now(),
			})
		}
	}
}

func (c *Client) handleHeartbeat() {
	c.mu.Lock()
	c.stats.LastHeartbeat = time.Now()
	c.mu.Unlock()

	// 响应心跳
	heartbeatMsg := NewMessage(MsgTypeHeartbeat, nil)
	if data, err := heartbeatMsg.Marshal(); err == nil {
		c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
		c.conn.WriteMessage(websocket.BinaryMessage, data)
	}
}

func (c *Client) heartbeatLoop() {
	defer c.wg.Done()

	ticker := time.NewTicker(c.config.HeartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			if c.IsConnected() {
				heartbeatMsg := NewMessage(MsgTypeHeartbeat, nil)
				if data, err := heartbeatMsg.Marshal(); err == nil {
					c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
					c.conn.WriteMessage(websocket.BinaryMessage, data)
				}
			}
		}
	}
}

func (c *Client) emitEvent(event Event) {
	select {
	case c.eventChan <- event:
	default:
		// 事件通道满，丢弃事件
	}
}
