package qt_websocket_hub

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

// Client HTTP客户端
type Client struct {
	config     *ClientConfig
	httpClient *http.Client
	baseURL    string

	// 统计信息
	stats ClientStats
	mu    sync.RWMutex
}

// NewClient 创建新的HTTP客户端
func NewClient(config *ClientConfig) *Client {
	if config == nil {
		config = DefaultConfig("")
	}

	// 确保URL格式正确
	baseURL := strings.TrimSuffix(config.ServerURL, "/")
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
	}

	return &Client{
		config:     config,
		httpClient: httpClient,
		baseURL:    baseURL,
		stats:      ClientStats{},
	}
}

// CreateDevice 创建设备ID
func (c *Client) CreateDevice(tags []string) (string, error) {
	req := DeviceCreateRequest{
		Tags:      tags,
		UserAgent: c.config.UserAgent,
	}

	var resp DeviceCreateResponse
	if err := c.doRequest("POST", "/api/v1/devices", req, &resp); err != nil {
		return "", fmt.Errorf("failed to create device: %w", err)
	}

	return resp.DeviceID, nil
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(method, path string, reqBody interface{}, respBody interface{}) error {
	c.mu.Lock()
	c.stats.RequestsTotal++
	c.stats.LastRequestTime = time.Now()
	c.mu.Unlock()

	var body io.Reader
	if reqBody != nil {
		jsonData, err := json.Marshal(reqBody)
		if err != nil {
			c.recordError(fmt.Errorf("failed to marshal request: %w", err))
			return err
		}
		body = bytes.NewBuffer(jsonData)
	}

	url := c.baseURL + path
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		c.recordError(fmt.Errorf("failed to create request: %w", err))
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", c.config.UserAgent)

	if c.config.Debug {
		fmt.Printf("[DEBUG] %s %s\n", method, url)
		if reqBody != nil {
			if jsonData, err := json.MarshalIndent(reqBody, "", "  "); err == nil {
				fmt.Printf("[DEBUG] Request: %s\n", string(jsonData))
			}
		}
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.recordError(fmt.Errorf("HTTP request failed: %w", err))
		return err
	}
	defer resp.Body.Close()

	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		c.recordError(fmt.Errorf("failed to read response: %w", err))
		return err
	}

	if c.config.Debug {
		fmt.Printf("[DEBUG] Response Status: %d\n", resp.StatusCode)
		fmt.Printf("[DEBUG] Response: %s\n", string(respData))
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		c.recordError(fmt.Errorf("HTTP error: %d %s", resp.StatusCode, resp.Status))
		return fmt.Errorf("HTTP error: %d %s", resp.StatusCode, resp.Status)
	}

	if respBody != nil {
		if err := json.Unmarshal(respData, respBody); err != nil {
			c.recordError(fmt.Errorf("failed to unmarshal response: %w", err))
			return err
		}
	}

	c.mu.Lock()
	c.stats.RequestsSuccess++
	c.mu.Unlock()

	return nil
}

// SendUnicast 发送单播消息
func (c *Client) SendUnicast(targetDeviceID string, data []byte) error {
	return c.sendMessage("unicast", targetDeviceID, nil, data, false)
}

// SendUnicastDistributed 发送分布式单播消息
func (c *Client) SendUnicastDistributed(targetDeviceID string, data []byte) error {
	return c.sendMessage("unicast", targetDeviceID, nil, data, true)
}

// SendBroadcast 发送广播消息
func (c *Client) SendBroadcast(data []byte) error {
	return c.sendMessage("broadcast", "", nil, data, false)
}

// SendBroadcastDistributed 发送分布式广播消息
func (c *Client) SendBroadcastDistributed(data []byte) error {
	return c.sendMessage("broadcast", "", nil, data, true)
}

// SendTagcast 发送标签组播消息
func (c *Client) SendTagcast(tags []string, data []byte) error {
	return c.sendMessage("tagcast", "", tags, data, false)
}

// SendTagcastDistributed 发送分布式标签组播消息
func (c *Client) SendTagcastDistributed(tags []string, data []byte) error {
	return c.sendMessage("tagcast", "", tags, data, true)
}



// sendMessage 发送消息的内部实现
func (c *Client) sendMessage(msgType, targetDeviceID string, tags []string, data []byte, distributed bool) error {
	req := MessageSendRequest{
		Type:           msgType,
		TargetDeviceID: targetDeviceID,
		Tags:           tags,
		Data:           base64.StdEncoding.EncodeToString(data),
		Distributed:    distributed,
	}

	// 消息发送API返回的是简单的成功响应，不是标准的HTTPResponse格式
	var resp map[string]interface{}
	if err := c.doRequest("POST", "/api/v1/messages", req, &resp); err != nil {
		return fmt.Errorf("failed to send %s message: %w", msgType, err)
	}

	// 检查是否有错误信息
	if errorMsg, exists := resp["error"]; exists && errorMsg != nil && errorMsg != "" {
		return fmt.Errorf("server error: %s", errorMsg)
	}

	c.mu.Lock()
	c.stats.MessagesSent++
	c.mu.Unlock()

	return nil
}

// GetStats 获取客户端统计信息
func (c *Client) GetStats() ClientStats {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.stats
}

// GetServerStats 获取服务器统计信息
func (c *Client) GetServerStats() (*StatsResponse, error) {
	var stats StatsResponse
	if err := c.doRequest("GET", "/api/v1/stats", nil, &stats); err != nil {
		return nil, fmt.Errorf("failed to get server stats: %w", err)
	}
	return &stats, nil
}

// HealthCheck 健康检查
func (c *Client) HealthCheck() (*HealthResponse, error) {
	var health HealthResponse
	if err := c.doRequest("GET", "/api/v1/health", nil, &health); err != nil {
		return nil, fmt.Errorf("health check failed: %w", err)
	}
	return &health, nil
}

// recordError 记录错误
func (c *Client) recordError(err error) {
	c.mu.Lock()
	c.stats.RequestsFailed++
	c.stats.LastError = err
	c.mu.Unlock()
}

// SendString 发送字符串消息（便捷方法）
func (c *Client) SendString(msgType, target string, tags []string, message string) error {
	data := []byte(message)
	switch msgType {
	case "unicast":
		return c.SendUnicast(target, data)
	case "broadcast":
		return c.SendBroadcast(data)
	case "tagcast":
		return c.SendTagcast(tags, data)
	default:
		return fmt.Errorf("unsupported message type: %s", msgType)
	}
}

// SendJSON 发送JSON消息（便捷方法）
func (c *Client) SendJSON(msgType, target string, tags []string, obj interface{}) error {
	data, err := json.Marshal(obj)
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	switch msgType {
	case "unicast":
		return c.SendUnicast(target, data)
	case "broadcast":
		return c.SendBroadcast(data)
	case "tagcast":
		return c.SendTagcast(tags, data)
	default:
		return fmt.Errorf("unsupported message type: %s", msgType)
	}
}


