{"name": "qt-websocket-hub-js", "version": "1.0.0", "description": "A JavaScript WebSocket client for Qt WebSocket Hub with binary protocol support", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "prepublishOnly": "npm run build", "dev": "rollup -c -w"}, "keywords": ["websocket", "client", "binary", "protocol", "react", "javascript", "typescript", "real-time", "messaging"], "author": "Your Name <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/qt-websocket-client.git"}, "bugs": {"url": "https://github.com/your-username/qt-websocket-client/issues"}, "homepage": "https://github.com/your-username/qt-websocket-client#readme", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rollup": "^4.6.1", "rollup-plugin-dts": "^6.1.0", "ts-jest": "^29.1.1", "typescript": "^5.3.2"}, "dependencies": {}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"react": {"optional": true}}, "engines": {"node": ">=14.0.0"}}