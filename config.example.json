{"server": {"node_id": "ws-node-example-1", "host": "0.0.0.0", "port": 8080, "debug": false}, "kafka": {"enabled": true, "brokers": ["localhost:9092"], "topic": "websocket-hub", "group_id": "websocket-hub-group"}, "device": {"secret_key": "your-secret-key-change-in-production", "max_age": "24h", "cleanup_interval": "1h"}, "websocket": {"read_timeout": "60s", "write_timeout": "10s", "ping_interval": "30s", "max_message_size": 1048576, "read_buffer_size": 1024, "write_buffer_size": 1024}, "log": {"level": "info", "format": "json", "output": "stdout", "max_size": 100, "max_backups": 3, "max_age": 28, "compress": true}}