// 消息类型枚举
export enum MessageType {
  // 连接相关消息
  HANDSHAKE = 0x01,
  HANDSHAKE_RESP = 0x02,
  HEARTBEAT = 0x03,
  DISCONNECT = 0x04,

  // 数据传输消息
  UNICAST = 0x10,
  BROADCAST = 0x11,
  TAGCAST = 0x12,

  // 系统消息
  ERROR = 0xf0,
  ACK = 0xf1,
}

// 标志位定义
export enum MessageFlags {
  COMPRESSED = 1 << 0,
  ENCRYPTED = 1 << 1,
  RELIABLE = 1 << 2,
}

// 消息头结构
export interface MessageHeader {
  magic: number;
  version: number;
  type: MessageType;
  flags: number;
  reserved: number;
  payloadLen: number;
  timestamp: number;
  checksum: number;
}

// 消息结构
export interface Message {
  header: MessageHeader;
  payload: Uint8Array;
}

// 握手载荷
export interface HandshakePayload {
  deviceId?: string;
  tags: string[];
  clientInfo: string;
}

// 握手响应载荷
export interface HandshakeResponse {
  deviceId: string;
  status: string;
  timestamp: number;
}

// 单播消息载荷
export interface UnicastPayload {
  targetDeviceId: string;
  data: Uint8Array;
}

// 广播消息载荷
export interface BroadcastPayload {
  data: Uint8Array;
}

// 标签组播消息载荷
export interface TagcastPayload {
  tags: string[];
  data: Uint8Array;
}

// 错误消息载荷
export interface ErrorPayload {
  code: number;
  message: string;
}

// 客户端配置
export interface WebSocketClientConfig {
  url: string;
  tags?: string[];
  deviceId?: string;
  clientInfo?: string;
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  connectionTimeout?: number;
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// 事件类型
export interface WebSocketEvents {
  connect: (deviceId: string) => void;
  disconnect: (reason: string) => void;
  error: (error: Error) => void;
  message: (data: Uint8Array, type: MessageType) => void;
  unicast: (data: Uint8Array, sourceDeviceId?: string) => void;
  broadcast: (data: Uint8Array, sourceDeviceId?: string) => void;
  tagcast: (data: Uint8Array, tags: string[], sourceDeviceId?: string) => void;
  stateChange: (state: ConnectionState) => void;
}

// React Hook 配置
export interface UseWebSocketConfig extends WebSocketClientConfig {
  enabled?: boolean;
}

// React Hook 返回值
export interface UseWebSocketReturn {
  state: ConnectionState;
  deviceId: string | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: Error | null;
  connect: () => void;
  disconnect: () => void;
  sendUnicast: (targetDeviceId: string, data: Uint8Array | string) => void;
  sendBroadcast: (data: Uint8Array | string) => void;
  sendTagcast: (tags: string[], data: Uint8Array | string) => void;
  addEventListener: <K extends keyof WebSocketEvents>(
    event: K,
    listener: WebSocketEvents[K]
  ) => void;
  removeEventListener: <K extends keyof WebSocketEvents>(
    event: K,
    listener: WebSocketEvents[K]
  ) => void;
}

// 统计信息
export interface ConnectionStats {
  connectedAt: Date | null;
  reconnectCount: number;
  messagesSent: number;
  messagesReceived: number;
  lastHeartbeat: Date | null;
  lastError: Error | null;
}
