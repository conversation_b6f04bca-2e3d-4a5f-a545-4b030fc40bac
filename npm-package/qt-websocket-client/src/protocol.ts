import { Message, MessageHeader, MessageType } from './types';

// 协议常量
export const MESSAGE_MAGIC = 0x5757; // 'WW'
export const MESSAGE_VERSION = 0x01;
export const HEADER_SIZE = 16;

/**
 * 消息协议处理类
 */
export class MessageProtocol {
  /**
   * 创建新消息
   */
  static createMessage(type: MessageType, payload: Uint8Array): Message {
    const header: MessageHeader = {
      magic: MESSAGE_MAGIC,
      version: MESSAGE_VERSION,
      type,
      flags: 0,
      reserved: 0,
      payloadLen: payload.length,
      timestamp: Math.floor(Date.now() / 1000),
      checksum: 0,
    };

    const message: Message = { header, payload };
    header.checksum = this.calculateChecksum(message);
    
    return message;
  }

  /**
   * 序列化消息为二进制数据
   */
  static marshal(message: Message): Uint8Array {
    const buffer = new ArrayBuffer(HEADER_SIZE + message.payload.length);
    const view = new DataView(buffer);
    let offset = 0;

    // 写入头部
    view.setUint16(offset, message.header.magic, false); // big-endian
    offset += 2;
    view.setUint8(offset, message.header.version);
    offset += 1;
    view.setUint8(offset, message.header.type);
    offset += 1;
    view.setUint8(offset, message.header.flags);
    offset += 1;
    view.setUint8(offset, message.header.reserved);
    offset += 1;
    view.setUint32(offset, message.header.payloadLen, false);
    offset += 4;
    view.setUint32(offset, message.header.timestamp, false);
    offset += 4;
    view.setUint16(offset, message.header.checksum, false);
    offset += 2;

    // 写入载荷
    const uint8Array = new Uint8Array(buffer);
    uint8Array.set(message.payload, offset);

    return uint8Array;
  }

  /**
   * 从二进制数据反序列化消息
   */
  static unmarshal(data: Uint8Array): Message {
    if (data.length < HEADER_SIZE) {
      throw new Error('Data too short for message header');
    }

    const view = new DataView(data.buffer, data.byteOffset, data.byteLength);
    let offset = 0;

    // 读取头部
    const header: MessageHeader = {
      magic: view.getUint16(offset, false),
      version: view.getUint8(offset + 2),
      type: view.getUint8(offset + 3),
      flags: view.getUint8(offset + 4),
      reserved: view.getUint8(offset + 5),
      payloadLen: view.getUint32(offset + 6, false),
      timestamp: view.getUint32(offset + 10, false),
      checksum: view.getUint16(offset + 14, false),
    };

    // 验证魔数和版本
    if (header.magic !== MESSAGE_MAGIC) {
      throw new Error('Invalid message magic');
    }
    if (header.version !== MESSAGE_VERSION) {
      throw new Error('Unsupported message version');
    }

    // 验证载荷长度
    if (data.length - HEADER_SIZE !== header.payloadLen) {
      throw new Error('Payload length mismatch');
    }

    // 读取载荷
    const payload = data.slice(HEADER_SIZE, HEADER_SIZE + header.payloadLen);
    const message: Message = { header, payload };

    // 验证校验和
    const calculatedChecksum = this.calculateChecksum(message);
    if (calculatedChecksum !== header.checksum) {
      throw new Error('Checksum verification failed');
    }

    return message;
  }

  /**
   * 计算消息校验和
   */
  static calculateChecksum(message: Message): number {
    let sum = 0;

    // 计算头部校验和（除了校验和字段本身）
    sum += message.header.magic;
    sum += message.header.version;
    sum += message.header.type;
    sum += message.header.flags;
    sum += message.header.reserved;
    sum += message.header.payloadLen & 0xffff;
    sum += (message.header.payloadLen >> 16) & 0xffff;
    sum += message.header.timestamp & 0xffff;
    sum += (message.header.timestamp >> 16) & 0xffff;

    // 计算载荷校验和
    for (let i = 0; i < message.payload.length; i++) {
      sum += message.payload[i];
    }

    return sum & 0xffff;
  }

  /**
   * 验证消息是否有效
   */
  static isValid(message: Message): boolean {
    return (
      message.header.magic === MESSAGE_MAGIC &&
      message.header.version === MESSAGE_VERSION &&
      message.payload.length === message.header.payloadLen &&
      this.calculateChecksum(message) === message.header.checksum
    );
  }

  /**
   * 设置消息标志位
   */
  static setFlag(message: Message, flag: number): void {
    message.header.flags |= flag;
    message.header.checksum = this.calculateChecksum(message);
  }

  /**
   * 检查消息是否有指定标志位
   */
  static hasFlag(message: Message, flag: number): boolean {
    return (message.header.flags & flag) !== 0;
  }
}

/**
 * 工具函数：字符串转Uint8Array
 */
export function stringToUint8Array(str: string): Uint8Array {
  return new TextEncoder().encode(str);
}

/**
 * 工具函数：Uint8Array转字符串
 */
export function uint8ArrayToString(data: Uint8Array): string {
  return new TextDecoder().decode(data);
}

/**
 * 工具函数：JSON对象转Uint8Array
 */
export function jsonToUint8Array(obj: any): Uint8Array {
  return stringToUint8Array(JSON.stringify(obj));
}

/**
 * 工具函数：Uint8Array转JSON对象
 */
export function uint8ArrayToJson<T = any>(data: Uint8Array): T {
  return JSON.parse(uint8ArrayToString(data));
}
