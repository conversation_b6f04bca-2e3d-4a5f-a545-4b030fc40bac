// 导出主要类和函数
export { QtWebSocketClient } from './client';
export { useQtWebSocket, useQtWebSocketEvent, useQtWebSocketState } from './hooks';

// 导出协议相关
export { MessageProtocol, stringToUint8Array, uint8ArrayToString, jsonToUint8Array, uint8ArrayToJson } from './protocol';

// 导出类型定义
export type {
  WebSocketClientConfig,
  UseWebSocketConfig,
  UseWebSocketReturn,
  ConnectionState,
  MessageType,
  MessageFlags,
  WebSocketEvents,
  Message,
  MessageHeader,
  HandshakePayload,
  HandshakeResponse,
  UnicastPayload,
  BroadcastPayload,
  TagcastPayload,
  ErrorPayload,
  ConnectionStats,
} from './types';

// 导出枚举
export { ConnectionState, MessageType, MessageFlags } from './types';

// 默认导出
export default QtWebSocketClient;
