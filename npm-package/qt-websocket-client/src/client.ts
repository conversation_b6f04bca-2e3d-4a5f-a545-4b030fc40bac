import {
  WebSocketClientConfig,
  ConnectionState,
  WebSocketEvents,
  MessageType,
  HandshakePayload,
  HandshakeResponse,
  UnicastPayload,
  BroadcastPayload,
  TagcastPayload,
  ErrorPayload,
  ConnectionStats,
} from './types';
import {
  MessageProtocol,
  stringToUint8Array,
  uint8ArrayToString,
  jsonToUint8Array,
  uint8ArrayToJson,
} from './protocol';

/**
 * Qt WebSocket客户端
 */
export class QtWebSocketClient {
  private config: Required<WebSocketClientConfig>;
  private ws: WebSocket | null = null;
  private state: ConnectionState = ConnectionState.DISCONNECTED;
  private deviceId: string | null = null;
  private reconnectTimer: number | null = null;
  private heartbeatTimer: number | null = null;
  private connectionTimer: number | null = null;
  private reconnectAttempts = 0;
  private eventListeners: Partial<WebSocketEvents> = {};
  private stats: ConnectionStats = {
    connectedAt: null,
    reconnectCount: 0,
    messagesSent: 0,
    messagesReceived: 0,
    lastHeartbeat: null,
    lastError: null,
  };

  constructor(config: WebSocketClientConfig) {
    this.config = {
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      connectionTimeout: 10000,
      tags: [],
      clientInfo: 'Qt WebSocket Client v1.0.0',
      ...config,
    };
  }

  /**
   * 连接到WebSocket服务器
   */
  async connect(): Promise<void> {
    if (this.state === ConnectionState.CONNECTING || this.state === ConnectionState.CONNECTED) {
      return;
    }

    this.setState(ConnectionState.CONNECTING);
    this.clearTimers();

    try {
      this.ws = new WebSocket(this.config.url);
      this.ws.binaryType = 'arraybuffer';

      // 设置连接超时
      this.connectionTimer = window.setTimeout(() => {
        if (this.state === ConnectionState.CONNECTING) {
          this.handleError(new Error('Connection timeout'));
          this.ws?.close();
        }
      }, this.config.connectionTimeout);

      this.ws.onopen = () => this.handleOpen();
      this.ws.onmessage = (event) => this.handleMessage(event);
      this.ws.onclose = (event) => this.handleClose(event);
      this.ws.onerror = () => this.handleError(new Error('WebSocket connection error'));

    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.config.reconnect = false;
    this.clearTimers();

    if (this.ws && this.state === ConnectionState.CONNECTED) {
      // 发送断开连接消息
      try {
        const message = MessageProtocol.createMessage(MessageType.DISCONNECT, new Uint8Array(0));
        this.ws.send(MessageProtocol.marshal(message));
      } catch (error) {
        console.warn('Failed to send disconnect message:', error);
      }
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.setState(ConnectionState.DISCONNECTED);
    this.deviceId = null;
    this.stats.connectedAt = null;
  }

  /**
   * 发送单播消息
   */
  sendUnicast(targetDeviceId: string, data: Uint8Array | string): void {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    const payload: UnicastPayload = {
      targetDeviceId,
      data: typeof data === 'string' ? stringToUint8Array(data) : data,
    };

    const payloadData = jsonToUint8Array(payload);
    const message = MessageProtocol.createMessage(MessageType.UNICAST, payloadData);
    this.sendMessage(message);
  }

  /**
   * 发送广播消息
   */
  sendBroadcast(data: Uint8Array | string): void {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    const payload: BroadcastPayload = {
      data: typeof data === 'string' ? stringToUint8Array(data) : data,
    };

    const payloadData = jsonToUint8Array(payload);
    const message = MessageProtocol.createMessage(MessageType.BROADCAST, payloadData);
    this.sendMessage(message);
  }

  /**
   * 发送标签组播消息
   */
  sendTagcast(tags: string[], data: Uint8Array | string): void {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    const payload: TagcastPayload = {
      tags,
      data: typeof data === 'string' ? stringToUint8Array(data) : data,
    };

    const payloadData = jsonToUint8Array(payload);
    const message = MessageProtocol.createMessage(MessageType.TAGCAST, payloadData);
    this.sendMessage(message);
  }

  /**
   * 添加事件监听器
   */
  addEventListener<K extends keyof WebSocketEvents>(
    event: K,
    listener: WebSocketEvents[K]
  ): void {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    (this.eventListeners[event] as any[]).push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener<K extends keyof WebSocketEvents>(
    event: K,
    listener: WebSocketEvents[K]
  ): void {
    if (this.eventListeners[event]) {
      const listeners = this.eventListeners[event] as any[];
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取连接状态
   */
  getState(): ConnectionState {
    return this.state;
  }

  /**
   * 获取设备ID
   */
  getDeviceId(): string | null {
    return this.deviceId;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === ConnectionState.CONNECTED;
  }

  /**
   * 检查是否正在连接
   */
  isConnecting(): boolean {
    return this.state === ConnectionState.CONNECTING;
  }

  /**
   * 获取连接统计信息
   */
  getStats(): ConnectionStats {
    return { ...this.stats };
  }

  // 私有方法

  private setState(newState: ConnectionState): void {
    if (this.state !== newState) {
      this.state = newState;
      this.emit('stateChange', newState);
    }
  }

  private handleOpen(): void {
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }

    // 发送握手消息
    this.sendHandshake();
  }

  private sendHandshake(): void {
    const handshakePayload: HandshakePayload = {
      deviceId: this.config.deviceId,
      tags: this.config.tags,
      clientInfo: this.config.clientInfo,
    };

    const payloadData = jsonToUint8Array(handshakePayload);
    const message = MessageProtocol.createMessage(MessageType.HANDSHAKE, payloadData);
    this.sendMessage(message);
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data = new Uint8Array(event.data);
      const message = MessageProtocol.unmarshal(data);
      
      this.stats.messagesReceived++;
      this.emit('message', message.payload, message.header.type);

      switch (message.header.type) {
        case MessageType.HANDSHAKE_RESP:
          this.handleHandshakeResponse(message.payload);
          break;
        case MessageType.HEARTBEAT:
          this.handleHeartbeat();
          break;
        case MessageType.UNICAST:
          this.handleUnicastMessage(message.payload);
          break;
        case MessageType.BROADCAST:
          this.handleBroadcastMessage(message.payload);
          break;
        case MessageType.TAGCAST:
          this.handleTagcastMessage(message.payload);
          break;
        case MessageType.ERROR:
          this.handleErrorMessage(message.payload);
          break;
        default:
          console.warn('Unknown message type:', message.header.type);
      }
    } catch (error) {
      console.error('Failed to handle message:', error);
      this.handleError(error as Error);
    }
  }

  private handleHandshakeResponse(payload: Uint8Array): void {
    try {
      const response: HandshakeResponse = uint8ArrayToJson(payload);
      
      if (response.status === 'connected') {
        this.deviceId = response.deviceId;
        this.setState(ConnectionState.CONNECTED);
        this.stats.connectedAt = new Date();
        this.reconnectAttempts = 0;
        
        // 启动心跳
        this.startHeartbeat();
        
        this.emit('connect', this.deviceId);
      } else {
        throw new Error(`Handshake failed: ${response.status}`);
      }
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private handleHeartbeat(): void {
    this.stats.lastHeartbeat = new Date();
    
    // 响应心跳
    const message = MessageProtocol.createMessage(MessageType.HEARTBEAT, new Uint8Array(0));
    this.sendMessage(message);
  }

  private handleUnicastMessage(payload: Uint8Array): void {
    try {
      const unicastPayload: UnicastPayload = uint8ArrayToJson(payload);
      this.emit('unicast', unicastPayload.data);
    } catch (error) {
      console.error('Failed to handle unicast message:', error);
    }
  }

  private handleBroadcastMessage(payload: Uint8Array): void {
    try {
      const broadcastPayload: BroadcastPayload = uint8ArrayToJson(payload);
      this.emit('broadcast', broadcastPayload.data);
    } catch (error) {
      console.error('Failed to handle broadcast message:', error);
    }
  }

  private handleTagcastMessage(payload: Uint8Array): void {
    try {
      const tagcastPayload: TagcastPayload = uint8ArrayToJson(payload);
      this.emit('tagcast', tagcastPayload.data, tagcastPayload.tags);
    } catch (error) {
      console.error('Failed to handle tagcast message:', error);
    }
  }

  private handleErrorMessage(payload: Uint8Array): void {
    try {
      const errorPayload: ErrorPayload = uint8ArrayToJson(payload);
      const error = new Error(`Server error: ${errorPayload.message} (Code: ${errorPayload.code})`);
      this.handleError(error);
    } catch (error) {
      console.error('Failed to handle error message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    this.clearTimers();
    this.setState(ConnectionState.DISCONNECTED);
    
    const reason = event.reason || `Connection closed (code: ${event.code})`;
    this.emit('disconnect', reason);

    // 自动重连
    if (this.config.reconnect && this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  private handleError(error: Error): void {
    this.stats.lastError = error;
    this.setState(ConnectionState.ERROR);
    this.emit('error', error);
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return;
    }

    this.setState(ConnectionState.RECONNECTING);
    this.reconnectAttempts++;
    this.stats.reconnectCount++;

    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectTimer = null;
      this.connect();
    }, this.config.reconnectInterval);
  }

  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected()) {
        const message = MessageProtocol.createMessage(MessageType.HEARTBEAT, new Uint8Array(0));
        this.sendMessage(message);
      }
    }, this.config.heartbeatInterval);
  }

  private sendMessage(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const data = MessageProtocol.marshal(message);
      this.ws.send(data);
      this.stats.messagesSent++;
    } else {
      throw new Error('WebSocket is not open');
    }
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
  }

  private emit<K extends keyof WebSocketEvents>(
    event: K,
    ...args: Parameters<WebSocketEvents[K]>
  ): void {
    const listeners = this.eventListeners[event] as WebSocketEvents[K][] | undefined;
    if (listeners) {
      listeners.forEach(listener => {
        try {
          (listener as any)(...args);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }
}
