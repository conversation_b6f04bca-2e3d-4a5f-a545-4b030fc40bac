#!/bin/bash

# Qt WebSocket Client 发布脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the package root directory."
    exit 1
fi

# 检查是否有未提交的更改
if [ -n "$(git status --porcelain)" ]; then
    log_warn "You have uncommitted changes. Please commit or stash them before publishing."
    git status --short
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 获取当前版本
CURRENT_VERSION=$(node -p "require('./package.json').version")
log_info "Current version: $CURRENT_VERSION"

# 询问新版本
echo "Select version bump type:"
echo "1) patch (1.0.0 -> 1.0.1)"
echo "2) minor (1.0.0 -> 1.1.0)"
echo "3) major (1.0.0 -> 2.0.0)"
echo "4) custom"

read -p "Enter your choice (1-4): " -n 1 -r
echo

case $REPLY in
    1)
        VERSION_TYPE="patch"
        ;;
    2)
        VERSION_TYPE="minor"
        ;;
    3)
        VERSION_TYPE="major"
        ;;
    4)
        read -p "Enter custom version: " CUSTOM_VERSION
        VERSION_TYPE="$CUSTOM_VERSION"
        ;;
    *)
        log_error "Invalid choice"
        exit 1
        ;;
esac

# 运行预发布检查
log_step "Running pre-publish checks..."

# 检查Node.js版本
NODE_VERSION=$(node --version)
log_info "Node.js version: $NODE_VERSION"

# 安装依赖
log_step "Installing dependencies..."
npm ci

# 运行linting
log_step "Running linting..."
npm run lint

# 运行测试
log_step "Running tests..."
npm test

# 构建项目
log_step "Building project..."
npm run build

# 检查构建输出
if [ ! -f "dist/index.js" ] || [ ! -f "dist/index.d.ts" ]; then
    log_error "Build output not found. Build may have failed."
    exit 1
fi

log_info "Build successful. Output files:"
ls -la dist/

# 更新版本号
log_step "Updating version..."
if [ "$VERSION_TYPE" = "patch" ] || [ "$VERSION_TYPE" = "minor" ] || [ "$VERSION_TYPE" = "major" ]; then
    npm version $VERSION_TYPE --no-git-tag-version
else
    npm version $VERSION_TYPE --no-git-tag-version
fi

NEW_VERSION=$(node -p "require('./package.json').version")
log_info "New version: $NEW_VERSION"

# 更新CHANGELOG
log_step "Please update CHANGELOG.md with the new version changes."
read -p "Press Enter when you've updated the CHANGELOG..."

# 提交更改
log_step "Committing changes..."
git add package.json CHANGELOG.md
git commit -m "chore: bump version to $NEW_VERSION"

# 创建标签
git tag "v$NEW_VERSION"

# 确认发布
log_warn "Ready to publish version $NEW_VERSION"
log_info "This will:"
echo "  - Push commits and tags to git repository"
echo "  - Publish package to npm registry"
echo ""
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Publishing cancelled."
    # 回滚版本更改
    git reset --hard HEAD~1
    git tag -d "v$NEW_VERSION"
    exit 0
fi

# 推送到git
log_step "Pushing to git repository..."
git push origin main
git push origin "v$NEW_VERSION"

# 发布到npm
log_step "Publishing to npm..."

# 检查npm登录状态
if ! npm whoami > /dev/null 2>&1; then
    log_warn "You are not logged in to npm. Please login first."
    npm login
fi

# 发布
npm publish --access public

log_info "Successfully published qt-websocket-client@$NEW_VERSION!"

# 显示发布信息
echo ""
log_info "Package published successfully!"
echo "  📦 Package: qt-websocket-client@$NEW_VERSION"
echo "  🔗 npm: https://www.npmjs.com/package/qt-websocket-client"
echo "  📚 Install: npm install qt-websocket-client"
echo ""
log_info "Next steps:"
echo "  1. Update documentation if needed"
echo "  2. Announce the release"
echo "  3. Update dependent projects"
