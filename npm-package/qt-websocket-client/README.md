# Qt WebSocket Client

A JavaScript/TypeScript WebSocket client for Qt WebSocket Hub with binary protocol support. Perfect for React applications that need real-time communication.

## Features

- 🚀 **Binary Protocol**: Efficient binary message protocol, no JSON double parsing
- 🔐 **Secure**: Server-generated device IDs prevent malicious connections
- 🏷️ **Tag System**: Support for connection tags and tag-based message routing
- 📱 **React Ready**: Built-in React hooks for easy integration
- 🔄 **Auto Reconnect**: Automatic reconnection with configurable retry logic
- 💓 **Heartbeat**: Built-in heartbeat mechanism for connection health
- 📦 **TypeScript**: Full TypeScript support with comprehensive type definitions
- 🧪 **Well Tested**: Comprehensive test suite

## Installation

```bash
npm install qt-websocket-client
```

## Quick Start

### Basic Usage

```typescript
import { QtWebSocketClient, MessageType } from 'qt-websocket-client';

const client = new QtWebSocketClient({
  url: 'ws://localhost:8080/api/v1/ws',
  tags: ['user', 'mobile'],
  clientInfo: 'My App v1.0.0'
});

// Connect to server
await client.connect();

// Listen for messages
client.addEventListener('unicast', (data) => {
  console.log('Received unicast message:', new TextDecoder().decode(data));
});

client.addEventListener('broadcast', (data) => {
  console.log('Received broadcast message:', new TextDecoder().decode(data));
});

// Send messages
client.sendUnicast('target-device-id', 'Hello, specific device!');
client.sendBroadcast('Hello, everyone!');
client.sendTagcast(['admin', 'moderator'], 'Admin message');
```

### React Hook Usage

```tsx
import React, { useEffect } from 'react';
import { useQtWebSocket, useQtWebSocketEvent } from 'qt-websocket-client';

function ChatComponent() {
  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['chat', 'user'],
    enabled: true,
  });

  // Listen for broadcast messages
  useQtWebSocketEvent(ws, 'broadcast', (data) => {
    const message = new TextDecoder().decode(data);
    console.log('New chat message:', message);
  }, []);

  const sendMessage = () => {
    if (ws.isConnected) {
      ws.sendBroadcast('Hello from React!');
    }
  };

  return (
    <div>
      <div>Status: {ws.state}</div>
      <div>Device ID: {ws.deviceId}</div>
      <button onClick={sendMessage} disabled={!ws.isConnected}>
        Send Message
      </button>
      {ws.error && <div>Error: {ws.error.message}</div>}
    </div>
  );
}
```

## API Reference

### QtWebSocketClient

#### Constructor

```typescript
new QtWebSocketClient(config: WebSocketClientConfig)
```

#### Configuration Options

```typescript
interface WebSocketClientConfig {
  url: string;                    // WebSocket server URL
  tags?: string[];               // Connection tags
  deviceId?: string;             // Existing device ID (optional)
  clientInfo?: string;           // Client information
  reconnect?: boolean;           // Enable auto-reconnect (default: true)
  reconnectInterval?: number;    // Reconnect interval in ms (default: 3000)
  maxReconnectAttempts?: number; // Max reconnect attempts (default: 10)
  heartbeatInterval?: number;    // Heartbeat interval in ms (default: 30000)
  connectionTimeout?: number;    // Connection timeout in ms (default: 10000)
}
```

#### Methods

- `connect(): Promise<void>` - Connect to WebSocket server
- `disconnect(): void` - Disconnect from server
- `sendUnicast(targetDeviceId: string, data: Uint8Array | string): void` - Send message to specific device
- `sendBroadcast(data: Uint8Array | string): void` - Send message to all connected devices
- `sendTagcast(tags: string[], data: Uint8Array | string): void` - Send message to devices with specific tags
- `addEventListener<K>(event: K, listener: WebSocketEvents[K]): void` - Add event listener
- `removeEventListener<K>(event: K, listener: WebSocketEvents[K]): void` - Remove event listener
- `getState(): ConnectionState` - Get current connection state
- `getDeviceId(): string | null` - Get device ID
- `isConnected(): boolean` - Check if connected
- `getStats(): ConnectionStats` - Get connection statistics

#### Events

- `connect: (deviceId: string) => void` - Fired when connected
- `disconnect: (reason: string) => void` - Fired when disconnected
- `error: (error: Error) => void` - Fired on error
- `message: (data: Uint8Array, type: MessageType) => void` - Fired on any message
- `unicast: (data: Uint8Array) => void` - Fired on unicast message
- `broadcast: (data: Uint8Array) => void` - Fired on broadcast message
- `tagcast: (data: Uint8Array, tags: string[]) => void` - Fired on tagcast message
- `stateChange: (state: ConnectionState) => void` - Fired on state change

### React Hooks

#### useQtWebSocket

```typescript
const ws = useQtWebSocket(config: UseWebSocketConfig);
```

Returns an object with connection state, methods, and event handlers.

#### useQtWebSocketEvent

```typescript
useQtWebSocketEvent(ws, 'broadcast', (data) => {
  // Handle broadcast message
}, [dependencies]);
```

Hook for listening to specific WebSocket events with automatic cleanup.

#### useQtWebSocketState

```typescript
const state = useQtWebSocketState(ws);
```

Hook that returns detailed connection state information.

## Message Types

- `HANDSHAKE` (0x01) - Initial handshake
- `HANDSHAKE_RESP` (0x02) - Handshake response
- `HEARTBEAT` (0x03) - Heartbeat message
- `DISCONNECT` (0x04) - Disconnect message
- `UNICAST` (0x10) - Point-to-point message
- `BROADCAST` (0x11) - Broadcast message
- `TAGCAST` (0x12) - Tag-based multicast message
- `ERROR` (0xF0) - Error message
- `ACK` (0xF1) - Acknowledgment message

## Connection States

- `DISCONNECTED` - Not connected
- `CONNECTING` - Attempting to connect
- `CONNECTED` - Successfully connected
- `RECONNECTING` - Attempting to reconnect
- `ERROR` - Connection error

## Examples

### Chat Application

```tsx
import React, { useState, useEffect } from 'react';
import { useQtWebSocket, useQtWebSocketEvent } from 'qt-websocket-client';

function ChatApp() {
  const [messages, setMessages] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState('');

  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['chat'],
    clientInfo: 'Chat App v1.0.0',
  });

  useQtWebSocketEvent(ws, 'broadcast', (data) => {
    const message = new TextDecoder().decode(data);
    setMessages(prev => [...prev, message]);
  }, []);

  const sendMessage = () => {
    if (inputValue.trim() && ws.isConnected) {
      ws.sendBroadcast(inputValue);
      setInputValue('');
    }
  };

  return (
    <div>
      <div>Status: {ws.state}</div>
      <div>Device ID: {ws.deviceId}</div>
      
      <div style={{ height: '300px', overflow: 'auto', border: '1px solid #ccc' }}>
        {messages.map((msg, index) => (
          <div key={index}>{msg}</div>
        ))}
      </div>
      
      <div>
        <input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          disabled={!ws.isConnected}
        />
        <button onClick={sendMessage} disabled={!ws.isConnected}>
          Send
        </button>
      </div>
    </div>
  );
}
```

### Real-time Notifications

```tsx
import React from 'react';
import { useQtWebSocket, useQtWebSocketEvent } from 'qt-websocket-client';

function NotificationComponent() {
  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['notifications', 'user'],
  });

  useQtWebSocketEvent(ws, 'tagcast', (data, tags) => {
    if (tags.includes('notifications')) {
      const notification = new TextDecoder().decode(data);
      // Show notification
      if ('Notification' in window) {
        new Notification('New Message', { body: notification });
      }
    }
  }, []);

  return (
    <div>
      <div>Notification Status: {ws.isConnected ? 'Active' : 'Inactive'}</div>
    </div>
  );
}
```

## Development

```bash
# Install dependencies
npm install

# Build the package
npm run build

# Run tests
npm test

# Run linting
npm run lint

# Watch mode for development
npm run dev
```

## License

MIT

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Support

- GitHub Issues: [Report bugs or request features](https://github.com/your-username/qt-websocket-client/issues)
- Documentation: [Full API documentation](https://github.com/your-username/qt-websocket-client/wiki)
