# Qt WebSocket Client NPM Package

这个目录包含了一个可以发布到npmjs的JavaScript/TypeScript WebSocket客户端包，专门为Qt WebSocket Hub服务器设计。

## 📦 包概述

**包名**: `qt-websocket-client`  
**版本**: 1.0.0  
**描述**: 一个支持二进制协议的JavaScript WebSocket客户端，专为React项目快速集成而设计

## 🚀 主要特性

- 🔥 **二进制协议支持**: 高效的二进制消息协议，避免JSON双重解析
- 🔐 **安全连接**: 支持服务端生成的设备ID，防止恶意连接
- 🏷️ **标签系统**: 支持连接标签和基于标签的消息路由
- ⚛️ **React就绪**: 内置React Hooks，开箱即用
- 🔄 **自动重连**: 可配置的自动重连机制
- 💓 **心跳检测**: 内置心跳机制保持连接健康
- 📦 **TypeScript**: 完整的TypeScript支持和类型定义
- 🧪 **测试完备**: 全面的测试覆盖

## 📁 目录结构

```
qt-websocket-client/
├── src/                    # 源代码
│   ├── client.ts          # 主要WebSocket客户端类
│   ├── hooks.ts           # React Hooks
│   ├── protocol.ts        # 二进制协议处理
│   ├── types.ts           # TypeScript类型定义
│   ├── index.ts           # 主入口文件
│   └── __tests__/         # 测试文件
├── examples/               # 使用示例
│   ├── react-chat/        # React聊天应用示例
│   └── vanilla-js/        # 原生JavaScript示例
├── docs/                  # 文档
│   └── QUICK_START.md     # 快速开始指南
├── scripts/               # 构建和发布脚本
│   ├── build-and-test.sh  # 构建测试脚本
│   └── publish.sh         # 发布脚本
├── dist/                  # 构建输出（发布时生成）
├── package.json           # 包配置
├── tsconfig.json          # TypeScript配置
├── rollup.config.js       # 构建配置
├── jest.config.js         # 测试配置
├── .eslintrc.js          # 代码检查配置
└── README.md             # 包文档
```

## 🛠️ 开发和构建

### 安装依赖

```bash
cd npm-package/qt-websocket-client
npm install
```

### 开发命令

```bash
# 构建项目
npm run build

# 监听模式构建
npm run dev

# 运行测试
npm test

# 监听模式测试
npm run test:watch

# 代码检查
npm run lint

# 修复代码风格
npm run lint:fix
```

### 构建和测试

```bash
# 运行完整的构建和测试流程
./scripts/build-and-test.sh
```

## 📦 发布到NPM

### 准备发布

1. 确保所有测试通过
2. 更新版本号和CHANGELOG
3. 提交所有更改

### 发布命令

```bash
# 运行发布脚本（交互式）
./scripts/publish.sh
```

发布脚本会自动：
- 运行测试和构建
- 更新版本号
- 创建git标签
- 推送到git仓库
- 发布到npm

### 手动发布

```bash
# 构建
npm run build

# 发布
npm publish --access public
```

## 📚 使用示例

### 基本用法

```typescript
import { QtWebSocketClient } from 'qt-websocket-client';

const client = new QtWebSocketClient({
  url: 'ws://localhost:8080/api/v1/ws',
  tags: ['chat', 'user']
});

await client.connect();
client.sendBroadcast('Hello, World!');
```

### React Hook用法

```tsx
import { useQtWebSocket } from 'qt-websocket-client';

function ChatComponent() {
  const ws = useQtWebSocket({
    url: 'ws://localhost:8080/api/v1/ws',
    tags: ['chat'],
    enabled: true,
  });

  return (
    <div>
      <div>状态: {ws.state}</div>
      <button 
        onClick={() => ws.sendBroadcast('Hello!')}
        disabled={!ws.isConnected}
      >
        发送消息
      </button>
    </div>
  );
}
```

## 🔧 配置选项

```typescript
interface WebSocketClientConfig {
  url: string;                    // WebSocket服务器URL
  tags?: string[];               // 连接标签
  deviceId?: string;             // 现有设备ID（可选）
  clientInfo?: string;           // 客户端信息
  reconnect?: boolean;           // 启用自动重连
  reconnectInterval?: number;    // 重连间隔（毫秒）
  maxReconnectAttempts?: number; // 最大重连次数
  heartbeatInterval?: number;    // 心跳间隔（毫秒）
  connectionTimeout?: number;    // 连接超时（毫秒）
}
```

## 🎯 消息类型

- **Broadcast**: 广播消息到所有连接的客户端
- **Unicast**: 发送消息到特定设备
- **Tagcast**: 发送消息到具有特定标签的设备

## 📊 连接状态

- `DISCONNECTED`: 未连接
- `CONNECTING`: 正在连接
- `CONNECTED`: 已连接
- `RECONNECTING`: 正在重连
- `ERROR`: 连接错误

## 🧪 测试

包含完整的测试套件：

```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- protocol.test.ts

# 生成覆盖率报告
npm test -- --coverage
```

## 📖 文档

- [快速开始指南](qt-websocket-client/docs/QUICK_START.md)
- [完整API文档](qt-websocket-client/README.md)
- [React示例](qt-websocket-client/examples/react-chat/)
- [原生JS示例](qt-websocket-client/examples/vanilla-js/)

## 🔗 相关链接

- **主项目**: [Qt WebSocket Hub](../)
- **NPM包**: `npm install qt-websocket-client`
- **GitHub**: (待发布后更新)

## 📄 许可证

MIT License - 详见 [LICENSE](qt-websocket-client/LICENSE) 文件

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📞 支持

- 🐛 [报告问题](https://github.com/your-username/qt-websocket-client/issues)
- 💬 [讨论](https://github.com/your-username/qt-websocket-client/discussions)
- 📧 邮箱支持: <EMAIL>
